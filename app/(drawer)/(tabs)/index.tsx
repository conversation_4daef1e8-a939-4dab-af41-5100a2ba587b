import { FontAwesome, Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useRouter, useNavigation, useFocusEffect } from 'expo-router';
import * as Location from 'expo-location';
import {
  Pressable,
  View,
  Text,
  Image,
  Modal,
  StyleSheet,
  Platform,
  Keyboard,
  KeyboardEvent,
} from 'react-native';
import { useCallback, useEffect, useRef, useState } from 'react';
import EventMarkerSheet from '~/components/Event/EventMarkerSheet';
import EventsScroll from '~/components/Event/EventsScroll';
import MapEventCategories from '~/components/Event/MapEventsCategories';
import Map from '~/components/Map/Map';
import MapActions from '~/components/Map/MapActions';
import PeopleScroll from '~/components/People/PeopleScroll';
import { Button, ButtonText } from '~/components/ui/button';
import { useColorScheme } from '~/lib/useColorScheme';
import { EventProvider, useEvent } from '~/providers/MapProvider';
import { MapTsTypeMap, ViewModeMap } from '~/types';
import Svg, { G, Path } from 'react-native-svg';
import BottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import { RenderBackdrop } from '~/components/RenderBackdrop';
import { Input, InputField } from '~/components/ui/input';
import SearchBottomSheet from '~/components/Map/SearchBottomSheet';
import { BlurView } from 'expo-blur';
import PersonProfileSheet from '~/components/People/PersonProfileSheet';
import { moderateScale } from 'react-native-size-matters';
import { UserService } from '~/services/UserService';
import { FileService } from '~/services/FileService';
import { AuthStore, UserStore } from '~/store/store';
import { Toast } from 'toastify-react-native';

function HomeComponent() {
  const navigation = useNavigation();
  const { colors, isDark } = useColorScheme();
  const router = useRouter();
  const {
    ViewMode,
    MapType,
    selectedEvent,
    personProfileSheetRef,
    isPersonProfileSheetOpen,
    userId,
  } = useEvent();
  const [menuOpen, setMenuOpen] = useState(false);
  const [searchSheetVisible, setSearchSheetVisible] = useState(false);

  const [upForText, setUpForText] = useState('');
  const userData = UserStore((state) => state.user);
  const [currentUpFor, setCurrentUpFor] = useState(userData?.upFor || '');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [removingUpFor, setRemovingUpFor] = useState(false);
  const [locationPermissionGranted, setLocationPermissionGranted] = useState(false);
  const [hasStories, setHasStories] = useState(false);

  // Bottom sheet references
  const upForBottomSheetRef = useRef(null);

  // Bottom sheet snap points
  const [upForSnapPoints, setUpForSnapPoints] = useState([300]); // Default snap point in pixels

  // Open/close bottom sheets
  const openUpForSheet = useCallback(() => {
    upForBottomSheetRef.current?.expand();
  }, []);

  const closeUpForSheet = useCallback(() => {
    upForBottomSheetRef.current?.close();
  }, []);

  useEffect(() => {
    const onKeyboardShow = (event: KeyboardEvent) => {
      const keyboardHeight = event.endCoordinates.height;
      setUpForSnapPoints([300 + keyboardHeight]); // Adjust snap point based on keyboard height
    };

    const onKeyboardHide = () => {
      setUpForSnapPoints([300]); // Reset to default snap point
    };

    const keyboardShowListener = Keyboard.addListener('keyboardDidShow', onKeyboardShow);
    const keyboardHideListener = Keyboard.addListener('keyboardDidHide', onKeyboardHide);

    return () => {
      keyboardShowListener.remove();
      keyboardHideListener.remove();
    };
  }, []);

  const handleUpForSubmit = async () => {
    if (upForText.trim().length > 0) {
      setIsSubmitting(true); // Start loading
      setCurrentUpFor(upForText);

      const payload = {
        userId: userData.id,
        username: userData.username,
        phoneNumber: userData.phoneNumber,
        email: userData.email,
        fullName: userData.fullName,
        gender: userData.gender,
        interests: userData.interests,
        eventPreferences: userData.eventPreferences,
        upFor: upForText,
        bio: userData.bio,
        visibility: userData.visibility,
      };

      try {
        const profileData = await UserService.updateProfile(payload);
        (UserStore.getState() as { setUser: (data) => void }).setUser(profileData.body);
        Toast.show({
          type: 'success',
          text1: 'upFor',
          text2: 'Successfully updated upFor',
          position: 'bottom',
          theme: isDark ? 'dark' : 'light',
          backgroundColor: colors.background,
          autoHide: true,
        });
      } catch (error) {
        Toast.show({
          type: 'error',
          text1: 'Failed to update upFor',
          text2: error.message,
          position: 'bottom',
          theme: isDark ? 'dark' : 'light',
          backgroundColor: colors.background,
          autoHide: true,
        });
      } finally {
        setIsSubmitting(false); // End loading
        Keyboard.dismiss();
      }
      setUpForText('');
      closeUpForSheet();
    }
  };

  const handleRemoveUpFor = async () => {
    setRemovingUpFor(true); // Start loading
    setCurrentUpFor('');
    const payload = {
      userId: userData.id,
      username: userData.username,
      phoneNumber: userData.phoneNumber,
      email: userData.email,
      fullName: userData.fullName,
      gender: userData.gender,
      interests: userData.interests,
      eventPreferences: userData.eventPreferences,
      upFor: '',
      bio: userData.bio,
      visibility: userData.visibility,
    };

    try {
      const profileData = await UserService.updateProfile(payload);
      (UserStore.getState() as { setUser: (data) => void }).setUser(profileData.body);
      Toast.show({
        type: 'success',
        text1: 'upFor',
        text2: 'Successfully removed upFor',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Failed to remove upFor',
        text2: error.message,
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } finally {
      setRemovingUpFor(false); // End loading
    }
    closeUpForSheet();
  };

  const toggleMenu = () => {
    setMenuOpen((prevState) => !prevState);
  };

  const openSearchSheet = () => {
    setSearchSheetVisible(true);
  };

  const closeSearchSheet = () => {
    setSearchSheetVisible(false);
  };

  // Function to fetch user stories
  const fetchUserStories = async () => {
    if (!userData?.id) return;

    try {
      const response = await FileService.getUserStories(userData.id);
      if (response.success && response.body && response.body.length > 0) {
        // Check if any story data has story images
        const hasValidStories = response.body.some(
          (story) => story.storyImages && story.storyImages.length > 0
        );
        setHasStories(hasValidStories);
      } else {
        setHasStories(false);
      }
    } catch (error) {
      console.error('Error fetching user stories:', error);
      setHasStories(false);
    }
  };

  // Move navigation.setOptions to useEffect to avoid setState during render
  useEffect(() => {
    navigation.setOptions({
      headerShown: selectedEvent || userId ? false : true,
      title: '',
      headerTransparent: true,
      headerStyle: {
        backgroundColor: 'transparent', // Ensures full transparency
        elevation: 0, // Removes shadow (Android)
        shadowOpacity: 0, // Removes shadow (iOS)
      },
      headerLeft: () => {
        return (
          <View className="flex-row items-center">
            <Pressable className="ml-4" onPress={toggleMenu}>
              <View
                style={{
                  backgroundColor: colors.background,
                  ...(hasStories && {
                    borderWidth: 3,
                    borderColor: colors.primary,
                  }),
                }}
                className="relative h-[48px] w-[48px] items-center justify-center rounded-full shadow-md">
                <Image
                  source={{
                    uri:
                      userData?.profilePicture && userData.profilePicture.length > 0
                        ? userData.profilePicture[userData.profilePicture.length - 1].secureUrl
                        : 'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
                  }}
                  className="h-full w-full rounded-full"
                  resizeMode="cover"
                />
                <View
                  style={{
                    position: 'absolute',
                    bottom: -5,
                    right: -5,
                    backgroundColor: colors.primary,
                    borderRadius: 50,
                  }}
                  className="h-6 w-6 items-center justify-center">
                  <Ionicons
                    name="add"
                    size={20}
                    color="white"
                    style={{ transform: [{ scale: 0.8 }] }}
                  />
                </View>
              </View>
            </Pressable>
            {currentUpFor && (
              <View
                style={{
                  backgroundColor: colors.primary,
                  paddingHorizontal: 10,
                  paddingVertical: 4,
                  borderRadius: 16,
                  marginLeft: 8,
                }}>
                <Text
                  style={{ color: 'white', fontSize: 12, fontWeight: 'bold' }}
                  numberOfLines={1}>
                  {currentUpFor}
                </Text>
              </View>
            )}
          </View>
        );
      },
      headerRight: () => (
        <View className="z-0 flex-row items-center">
          <Pressable className="z-0" onPress={openSearchSheet}>
            <View className="mr-[16px] h-[48px] w-[48px] items-center justify-center overflow-hidden rounded-full border border-white/10 bg-black/40 p-[8px]">
              <BlurView className="absolute inset-0" intensity={50} tint="dark" />
              <Svg width={24} height={24} viewBox="0 0 24 24">
                <Path
                  fill="none"
                  stroke="white"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={3}
                  d="m21 21l-4.343-4.343m0 0A8 8 0 1 0 5.343 5.343a8 8 0 0 0 11.314 11.314"></Path>
              </Svg>
            </View>
          </Pressable>
        </View>
      ),
    });
  }, [navigation, toggleMenu, selectedEvent, userId, colors, openSearchSheet, hasStories]);

  // Fetch user profile on component mount
  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        const profileData = await UserService.getMyProfile();
        (UserStore.getState() as { setUser: (data) => void }).setUser(profileData.body);

        // Fetch user stories after profile is loaded
        await fetchUserStories();

        // Fetch and update location after profile is loaded
        await fetchAndUpdateLocation();
      } catch (error) {
        console.error('Error fetching user profile:', error);
      }
    };

    fetchUserProfile();
  }, []);

  // Refetch stories when userData changes
  useEffect(() => {
    if (userData?.id) {
      fetchUserStories();
    }
  }, [userData?.id]);

  // Refetch stories when the screen comes into focus (after creating/deleting stories)
  useFocusEffect(
    useCallback(() => {
      if (userData?.id) {
        fetchUserStories();
      }
    }, [userData?.id])
  );

  // Function to fetch user location and update profile
  const fetchAndUpdateLocation = async () => {
    try {
      // Request location permissions
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        console.log('Location permission denied');
        setLocationPermissionGranted(false);
        return;
      }

      setLocationPermissionGranted(true);

      // Get current location
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      const { latitude, longitude } = location.coords;

      // Update user profile with location
      const updatedUserData = {
        ...userData,
        location: [longitude, latitude], // MongoDB typically uses [longitude, latitude] format
      };

      const payload = {
        userId: updatedUserData.id,
        username: updatedUserData.username,
        phoneNumber: updatedUserData.phoneNumber,
        email: updatedUserData.email,
        fullName: updatedUserData.fullName,
        gender: updatedUserData.gender,
        interests: updatedUserData.interests,
        eventPreferences: updatedUserData.eventPreferences,
        location: updatedUserData.location,
        upFor: userData.upFor,
        bio: updatedUserData.bio,
        visibility: updatedUserData.visibility,
      };

      const profileData = await UserService.updateProfile(payload);
      (UserStore.getState() as { setUser: (data) => void }).setUser(profileData.body);

      console.log('Location updated successfully:', { latitude, longitude });
    } catch (error) {
      console.error('Error fetching or updating location:', error);
    }
  };

  return (
    <>
      {/* Move Modal outside of navigation options to ensure it's properly rendered */}
      <Modal
        visible={menuOpen}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setMenuOpen(false)}>
        <Pressable style={styles.backdrop} onPress={() => setMenuOpen(false)}>
          <View
            style={[styles.menuContainer, { backgroundColor: colors.grey5 }]}
            className="p-12"
            onStartShouldSetResponder={() => true}>
            {hasStories && (
              <>
                <Pressable
                  style={styles.menuItem}
                  className="p-2"
                  onPress={() => {
                    router.push('/(drawer)/story/view');
                    setMenuOpen(false);
                  }}>
                  <Svg width="24" height="24" viewBox="0 0 24 24">
                    <G
                      fill="none"
                      stroke={colors.foreground}
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2.2}
                      color={colors.foreground}>
                      <Path d="M12 2c5.524 0 10 4.478 10 10s-4.476 10-10 10m-3-.5a11 11 0 0 1-3.277-1.754m0-15.492A11.3 11.3 0 0 1 9 2.5m-7 7.746a9.6 9.6 0 0 1 1.296-3.305M2 13.754a9.6 9.6 0 0 0 1.296 3.305" />
                      <Path d="M8 16.5c2.073-2.198 5.905-2.301 8 0m-1.782-6.75c0 1.243-.996 2.25-2.226 2.25s-2.225-1.007-2.225-2.25s.996-2.25 2.226-2.25s2.225 1.007 2.225 2.25" />
                    </G>
                  </Svg>
                  <Text
                    className="font-medium"
                    style={[styles.menuItemText, { color: colors.foreground }]}>
                    View Your Story
                  </Text>
                </Pressable>
                <View style={[styles.separator, { backgroundColor: colors.grey4 }]} />
              </>
            )}

            <Pressable
              style={styles.menuItem}
              className="p-2"
              onPress={() => {
                router.push('/(drawer)/story/create');
                setMenuOpen(false);
              }}>
              <Svg width="24" height="24" viewBox="0 0 24 24">
                <G
                  fill="none"
                  stroke={colors.foreground}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2.2}
                  color={colors.foreground}>
                  <Path d="M12 2c5.524 0 10 4.478 10 10s-4.476 10-10 10m-3-.5a11 11 0 0 1-3.277-1.754m0-15.492A11.3 11.3 0 0 1 9 2.5m-7 7.746a9.6 9.6 0 0 1 1.296-3.305M2 13.754a9.6 9.6 0 0 0 1.296 3.305" />
                  <Path d="M8 16.5c2.073-2.198 5.905-2.301 8 0m-1.782-6.75c0 1.243-.996 2.25-2.226 2.25s-2.225-1.007-2.225-2.25s.996-2.25 2.226-2.25s2.225 1.007 2.225 2.25" />
                </G>
              </Svg>
              <Text
                className="font-medium"
                style={[styles.menuItemText, { color: colors.foreground }]}>
                Add Story
              </Text>
            </Pressable>
            <View style={[styles.separator, { backgroundColor: colors.grey4 }]} />
            <Pressable
              style={styles.menuItem}
              className="p-2"
              onPress={() => {
                openUpForSheet();
                setMenuOpen(false);
              }}>
              <Svg width="24" height="24" viewBox="0 0 24 24">
                <Path
                  fill="none"
                  stroke={colors.foreground}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2.2}
                  d="m12 20l-7.5-7.428A5 5 0 1 1 12 6.006a5 5 0 1 1 7.96 6.053M16 19h6m-3-3v6"></Path>
              </Svg>
              <Text
                className="font-medium"
                style={[styles.menuItemText, { color: colors.foreground }]}>
                Add Up For
              </Text>
            </Pressable>
          </View>
        </Pressable>
      </Modal>

      {/* Search Bottom Sheet */}
      <SearchBottomSheet visible={searchSheetVisible} onClose={closeSearchSheet} />

      {MapType === MapTsTypeMap.Events && <MapEventCategories />}
      <Map />
      {!isPersonProfileSheetOpen && <MapActions />}
      {ViewMode === ViewModeMap.ScrollView && MapType === MapTsTypeMap.Events && <EventsScroll />}
      {ViewMode === ViewModeMap.ScrollView && MapType === MapTsTypeMap.People && <PeopleScroll />}

      {MapType === MapTsTypeMap.Events && <EventMarkerSheet />}
      {MapType === MapTsTypeMap.People && <PersonProfileSheet />}

      {/* Up For Bottom Sheet */}
      <View
        style={{
          position: 'absolute',
          zIndex: 100,
          width: '100%',
          height: '100%',
          pointerEvents: 'box-none',
        }}>
        <BottomSheet
          ref={upForBottomSheetRef}
          index={-1}
          snapPoints={upForSnapPoints} // Use dynamic snap points
          enablePanDownToClose
          backdropComponent={RenderBackdrop}
          handleIndicatorStyle={{
            backgroundColor: isDark ? '#6b7280' : '#a1a1aa',
            width: 40,
          }}
          containerStyle={{
            zIndex: 100,
            elevation: 100,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
          }}
          backgroundStyle={{
            backgroundColor: isDark ? colors.background : '#fff',
          }}>
          <BottomSheetView className="p-4">
            <Text className={`mb-2 font-bold text-xl ${isDark ? 'text-white' : 'text-black'}`}>
              What are you up for?
            </Text>
            <Text className={`mb-4 text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
              Let others know what you're in the mood for (max 20 characters)
            </Text>

            <Input
              variant="outline"
              size="md"
              className={`my-4 h-14 rounded-xl border-0 `}
              style={{ backgroundColor: colors.grey5 }}>
              <InputField
                value={upForText}
                onChangeText={(text) => setUpForText(text.slice(0, 20))}
                placeholder={userData?.upFor ? userData?.upFor : "I'm up for..."}
                className={`font-medium placeholder:text-[${colors.grey}] ${isDark ? 'text-white' : 'text-black'}`}
                placeholderTextColor={isDark ? colors.grey : colors.grey}
                maxLength={20}
              />
            </Input>

            <Text
              className={`-mt-3 mb-4 text-right text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
              {upForText.length}/20
            </Text>

            <View className="mt-2 flex-row justify-between">
              <Button
                size="md"
                variant="outline"
                className={`mr-2 h-14 flex-1 rounded-xl ${isDark ? 'border-gray-600' : 'border-gray-300'}`}
                onPress={closeUpForSheet}>
                <ButtonText className={isDark ? 'font-bold text-white' : 'font-bold text-gray-700'}>
                  Cancel
                </ButtonText>
              </Button>
              <Button
                size="md"
                variant="solid"
                className={`ml-2 h-14 flex-1 rounded-xl ${isDark ? 'bg-violet-700' : 'bg-violet-600'}`}
                onPress={handleUpForSubmit}
                disabled={upForText.trim().length === 0 || isSubmitting}>
                <ButtonText className="font-bold text-white">
                  {isSubmitting ? 'Saving...' : 'Save'}
                </ButtonText>
              </Button>
            </View>

            {userData?.upFor?.length > 0 && (
              <Button
                size="md"
                variant="solid"
                className={`mt-4 h-14 rounded-xl ${isDark ? 'bg-red-700' : 'bg-red-600'}`}
                onPress={handleRemoveUpFor}
                disabled={removingUpFor}>
                <ButtonText className="font-bold text-white">
                  {removingUpFor ? 'Removing...' : 'Remove Current'}
                </ButtonText>
              </Button>
            )}
          </BottomSheetView>
        </BottomSheet>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.8)',
    zIndex: 10,
  },
  menuContainer: {
    position: 'absolute',
    top: moderateScale(Platform.OS === 'ios' ? 100 : 70), // Position below header with some padding
    left: 20,
    width: 200,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    padding: 8,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuItemText: {
    marginLeft: 12,
    fontSize: 16,
  },
  separator: {
    height: 1,
    width: '100%',
    marginVertical: 4,
  },
});

export default function Home() {
  return (
    <EventProvider>
      <HomeComponent />
    </EventProvider>
  );
}
