import { StatusBar } from 'expo-status-bar';
import { useNavigation, useRouter } from 'expo-router';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  Pressable,
  InteractionManager,
} from 'react-native';
import Svg, { G, Path } from 'react-native-svg';

import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import { RenderBackdrop } from '../../../components/RenderBackdrop';
import { useEvent } from '~/providers/MapProvider';
import { useColorScheme } from '~/lib/useColorScheme';
import { UserStore } from '~/store/store';
import { EventService } from '~/services/EventService';
import { format } from 'date-fns';

// Mock events data for attended events (until we have an API endpoint for this)
const attendedEvents = [
  {
    id: 2,
    name: 'Music Festival',
    date: 'June 22, 2025',
    image: 'https://images.unsplash.com/photo-1501281668745-f7f57925c3b4',
    attending: 74,
    created: false,
  },
];

export default function ProfileScreen() {
  const router = useRouter();
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const userData = UserStore((state: any) => state.user);
  const { People } = useEvent();

  // State for tab selection
  const [activeTab, setActiveTab] = useState('created'); // 'created', 'attended', 'saved'
  const [savedEvents, setSavedEvents] = useState([]);
  const [createdEvents, setCreatedEvents] = useState([]);
  const [isLoadingSavedEvents, setIsLoadingSavedEvents] = useState(false);
  const [isLoadingCreatedEvents, setIsLoadingCreatedEvents] = useState(false);

  // Fetch created events when the component mounts or when switching to created tab
  useEffect(() => {
    const fetchCreatedEvents = async () => {
      if (activeTab === 'created' && userData?.id && createdEvents.length === 0) {
        setIsLoadingCreatedEvents(true);
        try {
          const response = await EventService.getEvents({ userId: userData.id });
          if (response.success && response.body) {
            setCreatedEvents(response.body);
          }
        } catch (error) {
          console.error('Error fetching created events:', error);
          setCreatedEvents([]);
        } finally {
          setIsLoadingCreatedEvents(false);
        }
      }
    };

    fetchCreatedEvents();
  }, [activeTab, userData?.id]);

  // Fetch saved events when the component mounts or when switching to saved tab
  useEffect(() => {
    const fetchSavedEvents = async () => {
      if (activeTab === 'saved' && userData?.id && savedEvents.length === 0) {
        setIsLoadingSavedEvents(true);
        try {
          const response = await EventService.getSavedEvents(userData.id);
          if (response.success && response.body) {
            setSavedEvents(response.body.events);
          }
        } catch (error) {
          console.error('Error fetching saved events:', error);
          setSavedEvents([]);
        } finally {
          setIsLoadingSavedEvents(false);
        }
      }
    };

    fetchSavedEvents();
  }, [activeTab, userData?.id]);

  // Filter events based on tab
  const filteredEvents = (() => {
    if (activeTab === 'created') {
      return createdEvents.map((event) => ({
        ...event,
        name: event.title,
        date: event.startDateTime ? format(new Date(event.startDateTime), 'MMMM d, yyyy') : '',
        image:
          event.coverImages && event.coverImages.length > 0
            ? event.coverImages[event.coverImages.length - 1].secureUrl
            : 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
        attending: event.attendees?.length || 0,
        created: true,
        ticketsSold: event.attendees?.length || 0,
        revenue:
          event.ticketTypes?.reduce((total, ticket) => {
            const soldTickets =
              event.attendees?.filter((attendee) => attendee.ticketType === ticket.id).length || 0;
            return total + soldTickets * ticket.price;
          }, 0) || 0,
      }));
    }

    if (activeTab === 'saved') {
      return savedEvents.map((event) => ({
        ...event,
        name: event.title,
        date: event.startDateTime ? format(new Date(event.startDateTime), 'MMMM d, yyyy') : '',
        image:
          event.coverImages && event.coverImages.length > 0
            ? event.coverImages[event.coverImages.length - 1].secureUrl
            : 'https://images.unsplash.com/photo-1501281668745-f7f57925c3b4',
        attending: 0, // This would come from event attendees if available
        created: false,
        saved: true,
      }));
    }

    // For attended events, return the mock data for now
    return attendedEvents;
  })();
  const friends = People.slice(0, 6);
  // Navigate to edit profile page with optimized performance
  const handleEditProfile = useCallback(() => {
    // Use InteractionManager to delay navigation until animations complete
    InteractionManager.runAfterInteractions(() => {
      router.push('/Auth/editProfile');
    });
  }, [router]);

  const navigation = useNavigation();

  // Refs for bottom sheets
  const cancelEventSheetRef = useRef(null);
  const payoutSheetRef = useRef(null);
  const ratingSheetRef = useRef(null);

  // State for bottom sheet snap points
  const [cancelEventSnap, setCancelEventSnap] = useState(-1);
  const [payoutSnap, setPayoutSnap] = useState(-1);
  const [ratingSnap, setRatingSnap] = useState(-1);

  // Close all bottom sheets
  const closeAllSheets = () => {
    cancelEventSheetRef.current?.close();
    payoutSheetRef.current?.close();
    ratingSheetRef.current?.close();
  };

  return (
    <View className="flex-1 pt-12" style={{ backgroundColor: colors.background }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      <View className="flex-row items-center justify-between px-4 pb-4">
        <TouchableOpacity className="p-2" onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={isDark ? '#fff' : '#000'} />
        </TouchableOpacity>
        <Text className={`font-medium text-xl ${isDark ? 'text-white' : 'text-black'}`}>
          Profile
        </Text>
        <Pressable className="z-0" onPress={() => navigation?.toggleDrawer()}>
          <Svg width={24} height={24} viewBox="0 0 24 24">
            <G fill="none">
              <Path d="m12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035q-.016-.005-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427q-.004-.016-.017-.018m.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093q.019.005.029-.008l.004-.014l-.034-.614q-.005-.018-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014l-.034.614q.001.018.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01z"></Path>
              <Path
                fill={colors.foreground}
                strokeWidth={3}
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M20 17.5a1.5 1.5 0 0 1 .144 2.993L20 20.5H4a1.5 1.5 0 0 1-.144-2.993L4 17.5zm0-7a1.5 1.5 0 0 1 0 3H4a1.5 1.5 0 0 1 0-3zm0-7a1.5 1.5 0 0 1 0 3H4a1.5 1.5 0 1 1 0-3z"></Path>
            </G>
          </Svg>
        </Pressable>
      </View>

      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 40 }}>
        <View className="mb-6 items-center px-4">
          {/* Profile Image and Edit Button */}
          <View className="relative mb-4">
            <Image
              source={{
                uri:
                  userData?.profilePicture && userData.profilePicture.length > 0
                    ? userData.profilePicture[userData.profilePicture.length - 1].secureUrl
                    : 'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
              }}
              className="h-[120px] w-[120px] rounded-full"
            />
            <TouchableOpacity
              className="absolute bottom-0 right-0 h-9 w-9 items-center justify-center rounded-full border-[3px] border-white bg-violet-600"
              onPress={() =>
                router.push({
                  pathname: '/Auth/editProfile',
                  params: { initialSection: 'photo' },
                })
              }>
              <MaterialCommunityIcons name="camera" size={20} color="#fff" />
            </TouchableOpacity>
          </View>

          {/* Name and Bio */}
          <View className="mb-4 items-center">
            <Text className={`mb-1 font-bold text-2xl ${isDark ? 'text-white' : 'text-black'}`}>
              {userData.fullName}
            </Text>

            <Text
              className={`px-8 text-center text-base ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
              {userData.bio || 'Add a bio to let others know more about you.'}
            </Text>
          </View>

          {/* Stats */}
          <View className="mb-4 w-full flex-row justify-around py-4">
            <View className="items-center">
              <Text className={`font-bold text-xl ${isDark ? 'text-white' : 'text-black'}`}>
                {userData?.stats?.events || 0}
              </Text>
              <Text className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                Events
              </Text>
            </View>

            <View className="items-center">
              <Text className={`font-bold text-xl ${isDark ? 'text-white' : 'text-black'}`}>
                {userData?.stats?.followers || 0}
              </Text>
              <Text className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                Friends
              </Text>
            </View>
          </View>

          {/* Interests */}
          <View className="mb-4 w-full">
            <Text className={`mb-3 font-medium text-lg ${isDark ? 'text-white' : 'text-black'}`}>
              Interests
            </Text>
            <View className="flex-row flex-wrap">
              {userData.interests?.length > 0 ? (
                userData.interests.map((interest, index) => (
                  <View
                    key={index}
                    className={`mb-2 mr-2 rounded-2xl px-3 py-1.5 `}
                    style={{ backgroundColor: colors.grey5 }}>
                    <Text className={`text-sm ${isDark ? 'text-white' : 'text-black'}`}>
                      {interest}
                    </Text>
                  </View>
                ))
              ) : (
                <Text className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                  Add some interests to showcase your hobbies and passions.
                </Text>
              )}
            </View>
          </View>

          {/* Edit Profile Button */}
          <TouchableOpacity
            className={`mt-2 rounded-full px-6 py-2.5 `}
            onPress={handleEditProfile}
            style={{ backgroundColor: colors.primary }}>
            <Text className={`font-medium text-base text-white`}>Edit Profile</Text>
          </TouchableOpacity>
        </View>

        {/* Friends Section */}
        <View className="mb-6 px-4">
          <View className="mb-4 flex-row items-center justify-between">
            <Text className={`font-medium text-lg ${isDark ? 'text-white' : 'text-black'}`}>
              Friends
            </Text>
            <TouchableOpacity>
              <Text className="text-violet-600">See All</Text>
            </TouchableOpacity>
          </View>

          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {friends.length > 0 ? (
              friends.map((friend) => (
                <TouchableOpacity key={friend.id} className="relative mr-4 w-[70px] items-center">
                  <Image
                    source={{ uri: friend.profilePhoto }}
                    className="mb-1.5 h-[60px] w-[60px] rounded-full"
                  />
                  <Text
                    className={`text-center text-xs ${isDark ? 'text-white' : 'text-black'}`}
                    numberOfLines={1}>
                    {friend.name}
                  </Text>
                  <View
                    className={`absolute right-2 top-0.5 h-2.5 w-2.5 rounded-full border-[1.5px] border-white ${friend.isOnline ? 'bg-green-500' : 'bg-gray-400'}`}
                  />
                </TouchableOpacity>
              ))
            ) : (
              <Text className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                No friends to display. Connect with others to see them here.
              </Text>
            )}
          </ScrollView>
        </View>

        {/* Events Tabs */}
        <View className={`flex-row border-b `} style={{ borderBottomColor: colors.grey5 }}>
          <TouchableOpacity
            className={`flex-1 items-center py-3 `}
            onPress={() => setActiveTab('created')}>
            <MaterialCommunityIcons
              name="calendar-plus"
              size={24}
              color={activeTab === 'created' ? colors.primary : colors.grey}
            />
            <Text
              className={`mt-1 text-sm `}
              style={{ color: activeTab === 'created' ? colors.primary : colors.grey }}>
              Created
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            className={`flex-1 items-center py-3 `}
            onPress={() => setActiveTab('attended')}>
            <MaterialCommunityIcons
              name="calendar-check"
              size={24}
              color={activeTab === 'attended' ? colors.primary : colors.grey}
            />
            <Text
              className={`mt-1 text-sm `}
              style={{ color: activeTab === 'attended' ? colors.primary : colors.grey }}>
              Attended
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            className={`flex-1 items-center py-3 `}
            onPress={() => setActiveTab('saved')}>
            <Ionicons
              name="bookmark"
              size={24}
              color={activeTab === 'saved' ? colors.primary : colors.grey}
            />
            <Text
              className={`mt-1 text-sm `}
              style={{ color: activeTab === 'saved' ? colors.primary : colors.grey }}>
              Saved
            </Text>
          </TouchableOpacity>
        </View>

        {/* Events List */}
        <View className="mt-4 px-4">
          {isLoadingSavedEvents && activeTab === 'saved' ? (
            <View className="items-center justify-center py-10">
              <Text className={`text-base ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                Loading saved events...
              </Text>
            </View>
          ) : isLoadingCreatedEvents && activeTab === 'created' ? (
            <View className="items-center justify-center py-10">
              <Text className={`text-base ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                Loading created events...
              </Text>
            </View>
          ) : filteredEvents.length > 0 ? (
            filteredEvents.map((event) => (
              <TouchableOpacity
                key={event.id}
                className="mb-4 overflow-hidden rounded-xl shadow-sm"
                style={{ backgroundColor: colors.grey5 }}
                onPress={() => {
                  if (event.created) {
                    // Navigate to event dashboard for events you created
                    router.push({
                      pathname: '/Events/eventDashboard',
                      params: { eventId: event.id },
                    });
                  } else {
                    router.push({
                      pathname: '/Events/viewEvent',
                      params: { eventId: event.id },
                    });
                  }
                }}>
                <View className="flex-row">
                  <Image source={{ uri: event.image }} className="h-[100px] w-[100px]" />
                  <View className="flex-1 justify-center p-3">
                    <Text
                      className={`mb-2 font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}>
                      {event.name}
                    </Text>
                    <View>
                      <View className="mb-1 flex-row items-center">
                        <Ionicons name="calendar" size={16} color={isDark ? '#bbb' : '#666'} />
                        <Text
                          className={`ml-1.5 text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                          {event.date}
                        </Text>
                      </View>
                      <View className="flex-row items-center">
                        <Ionicons name="people" size={16} color={isDark ? '#bbb' : '#666'} />
                        <Text
                          className={`ml-1.5 text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                          {event.attending} attending
                        </Text>
                      </View>
                    </View>
                  </View>
                </View>

                {/* Event Management Section - Only for created events */}
                {event.created ? (
                  <View className="border-t p-3" style={{ borderTopColor: colors.grey4 }}>
                    {/* Stats Row */}
                    <View className="mb-3 flex-row justify-between">
                      {/* Ticket Sales */}
                      <View className="items-center">
                        <Text className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                          Tickets Sold
                        </Text>
                        <Text
                          className={`font-bold text-base ${isDark ? 'text-white' : 'text-black'}`}>
                          {event.ticketsSold || 0}
                        </Text>
                      </View>

                      {/* Revenue */}
                      <View className="items-center">
                        <Text className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                          Revenue
                        </Text>
                        <Text
                          className={`font-bold text-base ${isDark ? 'text-white' : 'text-black'}`}>
                          ${event.revenue || 0}
                        </Text>
                      </View>

                      {/* Manage Button */}
                      <TouchableOpacity
                        className="flex-row items-center rounded-full px-3 py-1"
                        style={{ backgroundColor: colors.primary }}
                        onPress={() => {
                          router.push({
                            pathname: '/Events/eventDashboard',
                            params: { eventId: event.id },
                          });
                        }}>
                        <MaterialCommunityIcons name="chart-box" size={16} color="#fff" />
                        <Text className="ml-1 font-medium text-xs text-white">Manage</Text>
                      </TouchableOpacity>
                    </View>

                    {/* Action Buttons Row */}
                    <View className="mt-2 flex-row justify-between">
                      {/* Show Payout Button only for paid events */}
                      {event.revenue > 0 ? (
                        <>
                          <TouchableOpacity
                            className="mr-2 flex-1 flex-row items-center justify-center rounded-full px-3 py-2"
                            style={{ backgroundColor: colors.primary }}
                            onPress={() => {
                              setPayoutSnap(0); // Open payout sheet
                            }}>
                            <Ionicons name="cash-outline" size={16} color="#fff" />
                            <Text className="ml-1 font-medium text-xs text-white">
                              Request Payout
                            </Text>
                          </TouchableOpacity>

                          <TouchableOpacity
                            className="flex-1 flex-row items-center justify-center rounded-full px-3 py-2"
                            style={{ backgroundColor: colors.destructive }}
                            onPress={() => {
                              setCancelEventSnap(0); // Open cancel event sheet
                            }}>
                            <Ionicons name="close-circle-outline" size={16} color="#fff" />
                            <Text className="ml-1 font-medium text-xs text-white">
                              Cancel Event
                            </Text>
                          </TouchableOpacity>
                        </>
                      ) : (
                        // Only show the cancel button for free events
                        <TouchableOpacity
                          className="flex-1 flex-row items-center justify-center rounded-full px-3 py-2"
                          style={{ backgroundColor: colors.destructive }}
                          onPress={() => {
                            setCancelEventSnap(0); // Open cancel event sheet
                          }}>
                          <Ionicons name="close-circle-outline" size={16} color="#fff" />
                          <Text className="ml-1 font-medium text-xs text-white">Cancel Event</Text>
                        </TouchableOpacity>
                      )}
                    </View>
                  </View>
                ) : (
                  // For attended events, show the rate button
                  <View className="border-t p-3" style={{ borderTopColor: colors.grey4 }}>
                    <TouchableOpacity
                      className="flex-row items-center justify-center rounded-full px-4 py-2"
                      style={{ backgroundColor: colors.primary }}
                      onPress={() => {
                        setRatingSnap(0); // Open rating sheet
                      }}>
                      <Ionicons name="star" size={16} color="#fff" />
                      <Text className="ml-1 font-medium text-xs text-white">Rate Event</Text>
                    </TouchableOpacity>
                  </View>
                )}
              </TouchableOpacity>
            ))
          ) : (
            <View className="items-center justify-center py-10">
              <MaterialCommunityIcons
                name={
                  activeTab === 'created'
                    ? 'calendar-plus'
                    : activeTab === 'attended'
                      ? 'calendar-check'
                      : 'bookmark'
                }
                size={60}
                color={isDark ? '#444' : '#ddd'}
              />
              <Text
                className={`mb-5 mt-4 text-center text-base ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                {activeTab === 'created'
                  ? "You haven't created any events yet"
                  : activeTab === 'attended'
                    ? "You haven't attended any events yet"
                    : "You haven't saved any events yet"}
              </Text>
              {activeTab === 'created' && (
                <TouchableOpacity
                  className="rounded-full bg-violet-600 px-5 py-2.5"
                  onPress={() => router.push('/Events/createEvent')}>
                  <Text className="font-medium text-base text-white">Create Event</Text>
                </TouchableOpacity>
              )}
            </View>
          )}
        </View>
      </ScrollView>
      {/* Cancel Event Bottom Sheet */}
      <BottomSheet
        ref={cancelEventSheetRef}
        index={cancelEventSnap}
        onChange={setCancelEventSnap}
        snapPoints={['50%']}
        handleIndicatorStyle={{
          backgroundColor: colors.foreground,
        }}
        backgroundStyle={{ backgroundColor: colors.background }}
        backdropComponent={RenderBackdrop}>
        <BottomSheetView
          className="flex-1 justify-center"
          style={{ backgroundColor: colors.background }}>
          <Text
            className={`mb-4 text-center font-medium text-lg ${isDark ? 'text-white' : 'text-black'}`}>
            Cancel Event
          </Text>
          <Text
            className={`mb-8 text-center text-base ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Are you sure you want to cancel this event? All registered attendees will be notified.
          </Text>

          {/* Action Buttons */}
          <View className="flex-row justify-center">
            <TouchableOpacity
              className="mr-4 rounded-full px-6 py-3"
              onPress={() => {
                // Handle cancel event logic
                closeAllSheets();
              }}
              style={{ backgroundColor: colors.primary }}>
              <Text className={`font-medium text-base text-white`}>Yes, Cancel</Text>
            </TouchableOpacity>

            <TouchableOpacity
              className="rounded-full px-6 py-3"
              onPress={closeAllSheets}
              style={{ backgroundColor: colors.grey4 }}>
              <Text className={`font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}>
                No, Go Back
              </Text>
            </TouchableOpacity>
          </View>
        </BottomSheetView>
      </BottomSheet>

      {/* Payout Bottom Sheet */}
      <BottomSheet
        ref={payoutSheetRef}
        index={payoutSnap}
        onChange={setPayoutSnap}
        snapPoints={['60%']}
        handleIndicatorStyle={{
          backgroundColor: colors.foreground,
        }}
        backgroundStyle={{ backgroundColor: colors.background }}
        backdropComponent={RenderBackdrop}>
        <BottomSheetView
          className="flex-1 justify-center px-4"
          style={{ backgroundColor: colors.background }}>
          <Text
            className={`mb-4 text-center font-medium text-lg ${isDark ? 'text-white' : 'text-black'}`}>
            Request Payout
          </Text>
          <Text
            className={`mb-8 text-center text-base ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            Are you sure you want to request a payout for this event?
          </Text>

          {/* Total Amount Display */}
          <View className="mb-8 items-center">
            <Text className={`mb-2 text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
              Total Available for Payout
            </Text>
            <Text className={`font-bold text-3xl ${isDark ? 'text-white' : 'text-black'}`}>
              $405.00
            </Text>
          </View>

          {/* Action Buttons */}
          <View className="flex-row justify-center">
            <TouchableOpacity
              className="mr-4 rounded-full px-6 py-3"
              onPress={() => {
                // Handle payout request
                closeAllSheets();
              }}
              style={{ backgroundColor: colors.primary }}>
              <Text className={`font-medium text-base text-white`}>Confirm Request</Text>
            </TouchableOpacity>

            <TouchableOpacity
              className="rounded-full px-6 py-3"
              onPress={closeAllSheets}
              style={{ backgroundColor: colors.grey4 }}>
              <Text className={`font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}>
                Cancel
              </Text>
            </TouchableOpacity>
          </View>
        </BottomSheetView>
      </BottomSheet>

      {/* Rating Bottom Sheet */}
      <BottomSheet
        ref={ratingSheetRef}
        index={ratingSnap}
        onChange={setRatingSnap}
        snapPoints={['50%']}
        handleIndicatorStyle={{
          backgroundColor: colors.foreground,
        }}
        backgroundStyle={{ backgroundColor: colors.background }}
        backdropComponent={RenderBackdrop}>
        <BottomSheetView
          className="flex-1 justify-center"
          style={{ backgroundColor: colors.background }}>
          <Text
            className={`mb-4 text-center font-medium text-lg ${isDark ? 'text-white' : 'text-black'}`}>
            Rate Event
          </Text>
          <Text
            className={`mb-8 text-center text-base ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
            How would you rate your experience at the event?
          </Text>

          {/* Star Rating Component */}
          <View className="mb-6 flex-row justify-center">
            {[1, 2, 3, 4, 5].map((star) => (
              <TouchableOpacity
                key={star}
                className="mx-1"
                onPress={() => {
                  // Handle star rating selection
                }}>
                <Ionicons
                  name={star <= 3 ? 'star' : 'star-outline'}
                  size={28}
                  color={star <= 3 ? '#FFD700' : '#ddd'}
                />
              </TouchableOpacity>
            ))}
          </View>

          {/* Action Buttons */}
          <View className="flex-row justify-center">
            <TouchableOpacity
              className="mr-4 rounded-full px-6 py-3"
              onPress={() => {
                // Handle submit rating logic
                closeAllSheets();
              }}
              style={{ backgroundColor: colors.primary }}>
              <Text className={`font-medium text-base text-white`}>Submit Rating</Text>
            </TouchableOpacity>

            <TouchableOpacity
              className="rounded-full px-6 py-3"
              onPress={closeAllSheets}
              style={{ backgroundColor: colors.grey4 }}>
              <Text className={`font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}>
                Cancel
              </Text>
            </TouchableOpacity>
          </View>
        </BottomSheetView>
      </BottomSheet>
    </View>
  );
}
