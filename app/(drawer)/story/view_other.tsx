import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { VideoView, useVideoPlayer } from 'expo-video';
import { useState, useRef, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Dimensions,
  Animated,
  ActivityIndicator,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Toast } from 'toastify-react-native';

import { useColorScheme } from '~/lib/useColorScheme';
import { FileService } from '~/services/FileService';
import { UserStore } from '~/store/store';
import { UserStoryData } from '~/types/story_type';

const STORY_DURATION = 5000; // 5 seconds per story (fallback for images)

export default function ViewStoryScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const targetUserId = params.userId as string;

  const insets = useSafeAreaInsets();
  const userData = UserStore((state: any) => state.user);
  const { colors, isDark } = useColorScheme();

  // State for story data and navigation
  const [storiesData, setStoriesData] = useState<UserStoryData[]>([]);
  const [currentUserIndex, setCurrentUserIndex] = useState(0);
  const [currentStoryIndex, setCurrentStoryIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [paused, setPaused] = useState(false);
  const [videoLoaded, setVideoLoaded] = useState(false);
  const [fetchError, setFetchError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isFetchingStories, setIsFetchingStories] = useState(true);
  const [videoDuration, setVideoDuration] = useState(0);
  const [videoPosition, setVideoPosition] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [hasLoadedAdditionalStories, setHasLoadedAdditionalStories] = useState(false);

  const progressAnim = useRef(new Animated.Value(0)).current;
  const progressAnimation = useRef<Animated.CompositeAnimation | null>(null);
  const positionUpdateInterval = useRef<number | null>(null);

  // Initialize video player for expo-video
  const player = useVideoPlayer('', (player) => {
    player.loop = false;
    player.muted = false;
  });

  // Cleanup function to stop all playback and clear intervals
  const cleanup = useCallback(() => {
    // Stop video playback
    if (player) {
      try {
        player.pause();
        player.replaceAsync(''); // Clear video source
      } catch (error) {
        console.log('Error cleaning up video player:', error);
      }
    }

    // Stop progress animation
    if (progressAnimation.current) {
      progressAnimation.current.stop();
      progressAnimation.current = null;
    }

    // Clear position tracking interval
    if (positionUpdateInterval.current) {
      clearInterval(positionUpdateInterval.current as unknown as NodeJS.Timeout);
      positionUpdateInterval.current = null;
    }

    // Reset progress bar
    progressAnim.setValue(0);
  }, [player, progressAnim]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  // Fetch user stories on component mount and every time screen comes into focus
  useFocusEffect(
    useCallback(() => {
      const fetchUserStories = async () => {
        if (!userData.id || !targetUserId) {
          setFetchError('No user data found');
          setLoading(false);
          setIsFetchingStories(false);
          return;
        }

        try {
          // Reset all states when starting fresh fetch
          setStoriesData([]);
          setCurrentUserIndex(0);
          setCurrentStoryIndex(0);
          setLoading(true);
          setIsFetchingStories(true);
          setFetchError(null);
          setPaused(false);
          setVideoLoaded(false);
          setHasLoadedAdditionalStories(false);

          // Stop any ongoing progress animation
          if (progressAnimation.current) {
            progressAnimation.current.stop();
          }

          // First, fetch the target user's stories
          const response = await FileService.getUserStories(targetUserId);

          if (response.success && response.body && response.body.length > 0) {
            setStoriesData(response.body);
            // Always start at the first story
            setCurrentUserIndex(0);
            setCurrentStoryIndex(0);
          } else {
            setFetchError('No stories found for this user');
          }
        } catch (error) {
          console.error('Error fetching user stories:', error);
          setFetchError('Failed to load stories');
        } finally {
          setLoading(false);
          setIsFetchingStories(false);
        }
      };

      fetchUserStories();
    }, [userData.id, targetUserId])
  );

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return `${diffInSeconds}s ago`;
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}m ago`;
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    } else {
      return `${Math.floor(diffInSeconds / 86400)}d ago`;
    }
  };

  // Update video source and handle playback for current story
  useEffect(() => {
    if (storiesData.length > 0 && currentUserIndex < storiesData.length) {
      const currentStory = storiesData[currentUserIndex].storyImages[currentStoryIndex];

      if (currentStory?.resourceType === 'video' && currentStory?.secureUrl) {
        console.log('Loading video:', currentStory.secureUrl);
        setIsTransitioning(true);
        setVideoLoaded(false);
        setLoading(true);
        setVideoDuration(0);
        setVideoPosition(0);

        // Stop any ongoing progress animation
        if (progressAnimation.current) {
          progressAnimation.current.stop();
        }

        // Clear position tracking
        if (positionUpdateInterval.current) {
          clearInterval(positionUpdateInterval.current as unknown as NodeJS.Timeout);
        }

        // Reset progress bar
        progressAnim.setValue(0);

        // Load the video
        player.replaceAsync(currentStory.secureUrl);

        // Fallback timeout to ensure video starts playing
        const fallbackTimeout = setTimeout(() => {
          if (!videoLoaded) {
            console.log('Video loading timeout, attempting to start anyway');
            setVideoLoaded(true);
            setLoading(false);
            setIsTransitioning(false);
            startProgressAnimation();
          }
        }, 3000); // 3 second timeout

        return () => {
          clearTimeout(fallbackTimeout);
        };
      } else if (currentStory?.resourceType === 'image') {
        // For images, we don't need video loading
        setIsTransitioning(false);
        setVideoLoaded(false);
        setLoading(false);
        setVideoDuration(0);
        setVideoPosition(0);

        // Clear any video-related intervals
        if (positionUpdateInterval.current) {
          clearInterval(positionUpdateInterval.current as unknown as NodeJS.Timeout);
        }
      }
    }
  }, [currentStoryIndex, currentUserIndex, storiesData, player]);

  // Handle video playback state with more aggressive play attempts
  useEffect(() => {
    if (storiesData.length > 0 && currentUserIndex < storiesData.length) {
      const currentStory = storiesData[currentUserIndex].storyImages[currentStoryIndex];

      if (currentStory?.resourceType === 'video') {
        if (!paused && videoLoaded) {
          console.log('Starting video playback');
          // Try multiple times to ensure video starts playing
          player.play();

          // Fallback: retry after a short delay
          setTimeout(() => {
            if (!paused) {
              player.play();
            }
          }, 100);
        } else if (paused) {
          console.log('Pausing video playback');
          player.pause();
        }
      }
    }
  }, [paused, videoLoaded, currentStoryIndex, currentUserIndex, storiesData, player]);

  const startProgressAnimation = () => {
    const currentStory = storiesData[currentUserIndex]?.storyImages[currentStoryIndex];

    if (currentStory?.resourceType === 'video' && videoDuration > 0) {
      // For videos, use actual video duration
      startVideoProgressTracking();
    } else {
      // For images, use default duration
      progressAnim.setValue(0);
      progressAnimation.current = Animated.timing(progressAnim, {
        toValue: 1,
        duration: STORY_DURATION,
        useNativeDriver: false,
      });

      progressAnimation.current.start(({ finished }) => {
        if (finished && !paused) {
          goToNextStory();
        }
      });
    }
  };

  const startVideoProgressTracking = () => {
    if (positionUpdateInterval.current) {
      clearInterval(positionUpdateInterval.current);
    }

    positionUpdateInterval.current = setInterval(() => {
      if (!paused && videoDuration > 0) {
        const progress = videoPosition / videoDuration;
        progressAnim.setValue(Math.min(progress, 1));

        // Check if video is near the end
        if (progress >= 0.98) {
          goToNextStory();
        }
      }
    }, 100) as unknown as number;
  };

  const pauseProgressAnimation = () => {
    if (progressAnimation.current) {
      progressAnimation.current.stop();
    }
    if (positionUpdateInterval.current) {
      clearInterval(positionUpdateInterval.current as unknown as NodeJS.Timeout);
    }
  };

  const resumeProgressAnimation = () => {
    const currentStory = storiesData[currentUserIndex]?.storyImages[currentStoryIndex];

    if (currentStory?.resourceType === 'video' && videoDuration > 0) {
      startVideoProgressTracking();
    } else {
      const currentValue = (progressAnim as any)._value;
      const remainingDuration = STORY_DURATION * (1 - currentValue);

      progressAnimation.current = Animated.timing(progressAnim, {
        toValue: 1,
        duration: remainingDuration,
        useNativeDriver: false,
      });

      progressAnimation.current.start(({ finished }) => {
        if (finished && !paused) {
          goToNextStory();
        }
      });
    }
  };

  const fetchAdditionalStories = async () => {
    if (hasLoadedAdditionalStories) return;

    try {
      setHasLoadedAdditionalStories(true);
      const response = await FileService.getOtherUserStories(userData.id, targetUserId);
      console.log('additional Stories', response);

      if (response.success && response.body) {
        const { unviewedStories, viewedStories } = response.body;

        // Prioritize unviewed stories, then viewed stories
        const additionalStories = [...(unviewedStories || []), ...(viewedStories || [])];

        if (additionalStories.length > 0) {
          setStoriesData((prev) => {
            const newStories = [...prev, ...additionalStories];
            console.log('Updated stories data:', newStories.length, 'total stories');
            return newStories;
          });
          return true; // Return success
        }
      }
      return false; // No additional stories
    } catch (error) {
      console.error('Error fetching additional stories:', error);
      return false;
    }
  };

  const goToPreviousStory = () => {
    if (currentStoryIndex > 0) {
      setCurrentStoryIndex(currentStoryIndex - 1);
    } else if (currentUserIndex > 0) {
      // Move to previous user's last story
      const prevUserStories = storiesData[currentUserIndex - 1];
      const prevStoriesArray = prevUserStories.coverImages || prevUserStories.storyImages;
      setCurrentUserIndex(currentUserIndex - 1);
      setCurrentStoryIndex(prevStoriesArray.length - 1);
    } else {
      // We're at the beginning, close the story viewer
      cleanup();
      router.back();
    }
  };

  const goToNextStory = async () => {
    // Stop current progress tracking
    if (progressAnimation.current) {
      progressAnimation.current.stop();
    }
    if (positionUpdateInterval.current) {
      clearInterval(positionUpdateInterval.current as unknown as NodeJS.Timeout);
    }
    const currentUserStories = storiesData[currentUserIndex];
    const currentStoriesArray = currentUserStories.coverImages || currentUserStories.storyImages;

    if (currentStoryIndex < currentStoriesArray.length - 1) {
      // Move to next story of current user
      setCurrentStoryIndex(currentStoryIndex + 1);
    } else if (currentUserIndex < storiesData.length - 1) {
      // Move to next user's first story
      setCurrentUserIndex(currentUserIndex + 1);
      setCurrentStoryIndex(0);
    } else {
      // We've reached the end of current stories, try to fetch additional stories
      if (!hasLoadedAdditionalStories) {
        const hasMoreStories = await fetchAdditionalStories();

        // Wait a bit for state to update and then check
        setTimeout(() => {
          setStoriesData((currentStories) => {
            if (currentStories.length > currentUserIndex + 1) {
              // We have more stories, move to next
              setTimeout(() => {
                setCurrentUserIndex(currentUserIndex + 1);
                setCurrentStoryIndex(0);
              }, 100);
            } else {
              // No more stories, close viewer
              cleanup();
              router.back();
            }
            return currentStories;
          });
        }, 300);
      } else {
        // No more stories available, close the viewer
        cleanup();
        router.back();
      }
    }
  };

  // Handle video status changes with proper error handling
  useEffect(() => {
    if (!player) return;

    const handleStatusChange = (status: any) => {
      console.log('Video status changed:', status);

      // Handle video loaded state and get duration
      if (status.isLoaded && !videoLoaded) {
        console.log('Video is loaded, setting videoLoaded to true');
        setVideoLoaded(true);
        setLoading(false);
        setIsTransitioning(false);

        // Get video duration if available
        if (status.durationMillis) {
          setVideoDuration(status.durationMillis);
        }

        startProgressAnimation();
      }

      // Update video position
      if (status.positionMillis !== undefined) {
        setVideoPosition(status.positionMillis);
      }

      // Update duration if it becomes available
      if (status.durationMillis && status.durationMillis !== videoDuration) {
        setVideoDuration(status.durationMillis);
      }

      // Handle video end - check multiple possible properties
      if (status.didJustFinish || status.hasFinishedPlaying || status.isFinished) {
        console.log('Video playback ended');
        goToNextStory();
      }

      // Handle video errors
      if (status.error) {
        console.error('Video error:', status.error);
        setLoading(false);
        setIsTransitioning(false);
        // Skip to next story on error
        setTimeout(() => {
          goToNextStory();
        }, 1000);
      }
    };

    // Add event listener for expo-video status changes
    let statusSubscription: any = null;

    try {
      statusSubscription = player.addListener('statusChange', handleStatusChange);
    } catch (error) {
      console.error('Failed to add status change listener:', error);
    }

    // Fallback: Periodic status checking for better reliability
    const statusCheckInterval = setInterval(() => {
      try {
        // Get current video status (expo-video might expose this differently)
        const currentStory =
          storiesData.length > 0 && currentUserIndex < storiesData.length
            ? storiesData[currentUserIndex].storyImages[currentStoryIndex]
            : null;

        if (currentStory?.resourceType === 'video' && !videoLoaded && !isTransitioning) {
          // Check if video has been playing for a bit and force start if needed
          console.log('Periodic check: Video should be loaded by now');
          setVideoLoaded(true);
          setLoading(false);
          setIsTransitioning(false);
          startProgressAnimation();
        }
      } catch (error) {
        console.error('Error in periodic status check:', error);
      }
    }, 2000); // Check every 2 seconds

    return () => {
      // Cleanup event listeners
      if (statusSubscription?.remove) {
        statusSubscription.remove();
      }
      clearInterval(statusCheckInterval);
      if (positionUpdateInterval.current) {
        clearInterval(positionUpdateInterval.current as unknown as NodeJS.Timeout);
      }
    };
  }, [
    player,
    videoLoaded,
    storiesData,
    currentUserIndex,
    currentStoryIndex,
    videoDuration,
    isTransitioning,
  ]);

  const handlePress = (event: { nativeEvent: { locationX: number } }) => {
    const { locationX } = event.nativeEvent;
    const screenWidth = Dimensions.get('window').width;

    if (locationX < screenWidth / 3) {
      goToPreviousStory();
    } else if (locationX > (screenWidth * 2) / 3) {
      goToNextStory();
    } else {
      // Toggle pause/play
      if (paused) {
        setPaused(false);
        resumeProgressAnimation();
      } else {
        setPaused(true);
        pauseProgressAnimation();
      }
    }
  };

  useEffect(() => {
    // Reset loading state when story or user changes
    setLoading(true);
    setIsTransitioning(true);
    if (progressAnimation.current) {
      progressAnimation.current.stop();
    }
    if (positionUpdateInterval.current) {
      clearInterval(positionUpdateInterval.current as unknown as NodeJS.Timeout);
    }
    // Reset progress bar
    progressAnim.setValue(0);
  }, [currentStoryIndex, currentUserIndex]);

  const handleClose = () => {
    cleanup();
    router.back();
  };

  const handleImageLoad = () => {
    console.log('Image loaded, starting progress animation');
    setLoading(false);
    setIsTransitioning(false);
    startProgressAnimation();
  };

  const handleDeleteStory = async () => {
    const currentUserStoryData = storiesData[currentUserIndex];
    const currentStoryUpload = currentUserStoryData.storyImages[currentStoryIndex];

    if (!currentStoryUpload || isDeleting) return;

    try {
      setIsDeleting(true);

      // Call delete story API
      await FileService.deleteStory({
        storyId: currentUserStoryData.id,
        userId: userData.id,
      });

      // Remove the deleted story from local state
      const updatedStoriesData = [...storiesData];
      updatedStoriesData[currentUserIndex].storyImages.splice(currentStoryIndex, 1);

      // If no more stories for this user, remove the user data
      if (updatedStoriesData[currentUserIndex].storyImages.length === 0) {
        updatedStoriesData.splice(currentUserIndex, 1);

        // If no more users with stories, go back
        if (updatedStoriesData.length === 0) {
          cleanup();
          router.back();
          return;
        }

        // Adjust indices if we removed a user
        if (currentUserIndex >= updatedStoriesData.length) {
          setCurrentUserIndex(updatedStoriesData.length - 1);
          setCurrentStoryIndex(0);
        } else {
          setCurrentStoryIndex(0);
        }
      } else {
        // Adjust story index if we're at the last story
        if (currentStoryIndex >= updatedStoriesData[currentUserIndex].storyImages.length) {
          setCurrentStoryIndex(updatedStoriesData[currentUserIndex].storyImages.length - 1);
        }
      }

      setStoriesData(updatedStoriesData);

      Toast.show({
        type: 'success',
        text1: 'Story Deleted',
        text2: 'Your story has been deleted successfully',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } catch (error) {
      console.error('Error deleting story:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to delete story. Please try again.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } finally {
      setIsDeleting(false);
    }
  };

  // Reset video loaded state when story changes
  useEffect(() => {
    setVideoLoaded(false);
    setVideoDuration(0);
    setVideoPosition(0);
  }, [currentStoryIndex]);

  // Show loading state for initial fetch
  if (isFetchingStories) {
    return (
      <View className="flex-1 items-center justify-center bg-black">
        <StatusBar style="light" />
        <ActivityIndicator size="large" color="#fff" />
        <Text className="mt-4 font-medium text-white">Loading stories...</Text>
      </View>
    );
  }

  // Show loading state
  if (loading && !storiesData.length) {
    return (
      <View className="flex-1 items-center justify-center bg-black">
        <StatusBar style="light" />
        <ActivityIndicator size="large" color="#fff" />
        <Text className="mt-4 font-medium text-white">Preparing story...</Text>
      </View>
    );
  }

  // Show error state
  if (fetchError || !storiesData.length) {
    return (
      <View className="flex-1 items-center justify-center bg-black">
        <StatusBar style="light" />
        <Text className="font-medium text-white">{fetchError || 'No stories found'}</Text>
        <TouchableOpacity
          className="mt-4 rounded-full bg-violet-600 px-6 py-3"
          onPress={handleClose}>
          <Text className="font-medium text-white">Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const currentUserStoryData = storiesData[currentUserIndex];
  const currentStoryUpload = currentUserStoryData.coverImages
    ? currentUserStoryData.coverImages[currentStoryIndex]
    : currentUserStoryData.storyImages[currentStoryIndex];

  const totalStories = storiesData.reduce((sum, userData) => {
    const storiesArray = userData.coverImages || userData.storyImages;
    return sum + storiesArray.length;
  }, 0);

  const currentStoryNumber =
    storiesData.slice(0, currentUserIndex).reduce((sum, userData) => {
      const storiesArray = userData.coverImages || userData.storyImages;
      return sum + storiesArray.length;
    }, 0) +
    currentStoryIndex +
    1;

  return (
    <View className="flex-1 bg-black">
      <StatusBar style={isDark ? 'light' : 'dark'} />

      {/* Progress bars */}
      <View
        className="absolute left-0 right-0 z-10 flex-row px-2 pb-2"
        style={{ paddingTop: insets.top + 10 }}>
        {(currentUserStoryData.coverImages || currentUserStoryData.storyImages).map((_, index) => (
          <View key={index} className="mx-0.5 h-1 flex-1 overflow-hidden rounded-full bg-gray-300">
            {index < currentStoryIndex ? (
              <View className="h-full bg-white" />
            ) : index === currentStoryIndex ? (
              <Animated.View
                className="h-full"
                style={{
                  width: progressAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  }),
                  backgroundColor: colors.primary,
                }}
              />
            ) : null}
          </View>
        ))}
      </View>

      {/* User info */}
      <View
        className="absolute left-0 right-0 top-5 z-10 flex-row items-center justify-between px-4"
        style={{ paddingTop: insets.top + 20 }}>
        <View className="flex-row items-center">
          <TouchableOpacity onPress={handleClose} className="mr-3">
            <Ionicons name="chevron-back" size={24} color={colors.foreground} />
          </TouchableOpacity>

          {/* Check if this is an event ad */}
          {currentUserStoryData.coverImages ? (
            // Event Ad Display
            <View className="flex-row items-center">
              <View className="mr-2 h-8 w-8 items-center justify-center rounded-full bg-violet-600">
                <Ionicons name="calendar" size={16} color="white" />
              </View>
              <View>
                <Text className="font-medium" style={{ color: colors.foreground }}>
                  {currentUserStoryData.title || 'Event'}
                </Text>
                <Text className="text-xs" style={{ color: colors.foreground }}>
                  Sponsored • {formatTimeAgo(currentStoryUpload?.createdAt)}
                </Text>
              </View>
            </View>
          ) : currentUserStoryData.user ? (
            // Regular User Story Display
            <View className="flex-row items-center">
              <Image
                source={{
                  uri:
                    currentUserStoryData.user?.profilePicture &&
                    currentUserStoryData.user.profilePicture.length > 0
                      ? currentUserStoryData.user.profilePicture[0].secureUrl
                      : 'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
                }}
                className="mr-2 h-8 w-8 rounded-full"
              />
              <View>
                <Text className="font-medium" style={{ color: colors.foreground }}>
                  {currentUserStoryData.user.fullName}
                </Text>
                <Text className="text-xs" style={{ color: colors.foreground }}>
                  {formatTimeAgo(currentStoryUpload?.createdAt)}
                </Text>
              </View>
            </View>
          ) : (
            // Story without user details
            <View className="flex-row items-center">
              <View className="mr-2 h-8 w-8 items-center justify-center rounded-full bg-gray-600">
                <Ionicons name="person" size={16} color="white" />
              </View>
              <View>
                <Text className="font-medium" style={{ color: colors.foreground }}>
                  Story
                </Text>
                <Text className="text-xs" style={{ color: colors.foreground }}>
                  {formatTimeAgo(currentStoryUpload?.createdAt)}
                </Text>
              </View>
            </View>
          )}
        </View>

        <View className="flex-row items-center">
          <Text className="mr-3 text-xs" style={{ color: colors.foreground }}>
            {currentStoryNumber} / {totalStories}
          </Text>
          {/* Only show delete button if viewing own story */}
          {currentUserStoryData.user?.id === userData.id && (
            <TouchableOpacity
              onPress={handleDeleteStory}
              disabled={isDeleting}
              className="h-8 w-8 items-center justify-center rounded-full bg-black/40">
              {isDeleting ? (
                <ActivityIndicator size="small" color={colors.foreground} />
              ) : (
                <Ionicons name="trash-outline" size={16} color={colors.foreground} />
              )}
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Story content */}
      <TouchableOpacity
        activeOpacity={1}
        onPress={handlePress}
        className="flex-1 justify-center"
        style={{
          backgroundColor: colors.background,
        }}>
        {(loading || isTransitioning) && (
          <View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'black',
              justifyContent: 'center',
              alignItems: 'center',
              zIndex: 10,
            }}>
            <ActivityIndicator size="large" color="#fff" />
          </View>
        )}

        {/* Show event ad overlay if this is an event */}
        {currentUserStoryData.coverImages && (
          <View className="absolute bottom-20 left-4 right-4 z-20 rounded-xl bg-black/80 p-4">
            <View className="flex-row items-center">
              <View className="mr-3 h-12 w-12 items-center justify-center rounded-full bg-violet-600">
                <Ionicons name="calendar" size={24} color="white" />
              </View>
              <View className="flex-1">
                <Text className="font-bold text-lg text-white">
                  {currentUserStoryData.title || 'Event'}
                </Text>
                {currentUserStoryData.description && (
                  <Text className="text-sm text-gray-300" numberOfLines={2}>
                    {currentUserStoryData.description}
                  </Text>
                )}
                {currentUserStoryData.location && (
                  <View className="mt-1 flex-row items-center">
                    <Ionicons name="location-outline" size={12} color="#9CA3AF" />
                    <Text className="ml-1 text-xs text-gray-400">
                      {currentUserStoryData.location}
                    </Text>
                  </View>
                )}
              </View>
              <TouchableOpacity className="rounded-full bg-violet-600 px-4 py-2">
                <Text className="font-semibold text-white">View Event</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {currentStoryUpload?.resourceType === 'image' ? (
          <Image
            source={{ uri: currentStoryUpload?.secureUrl }}
            style={{
              width: Dimensions.get('window').width,
              height: Dimensions.get('window').height,
            }}
            resizeMode="contain"
            onLoad={handleImageLoad}
          />
        ) : (
          <VideoView
            player={player}
            style={{
              width: Dimensions.get('window').width,
              height: Dimensions.get('window').height,
              opacity: isTransitioning ? 0 : 1,
            }}
            contentFit="cover"
            allowsFullscreen={false}
            allowsPictureInPicture={false}
          />
        )}
      </TouchableOpacity>
    </View>
  );
}
