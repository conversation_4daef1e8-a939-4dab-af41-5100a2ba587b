import { Ionicons, MaterialIcons, MaterialCommunityIcons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { useNavigation } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { VideoView, useVideoPlayer } from 'expo-video';
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  ScrollView,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { Toast } from 'toastify-react-native';

import { useColorScheme } from '~/lib/useColorScheme';
import { FileService } from '~/services/FileService';
import { UserStore } from '~/store/store';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default function CreateStoryScreen() {
  const navigation = useNavigation();
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const user = UserStore((state: any) => state.user);

  const [selectedMedia, setSelectedMedia] = useState<ImagePicker.ImagePickerAsset | null>(null);
  const [uploading, setUploading] = useState(false);
  const [showAspectRatios, setShowAspectRatios] = useState(false);
  const [selectedAspectRatio, setSelectedAspectRatio] = useState<[number, number]>([9, 16]);

  // Create video player for preview
  const videoPlayer = useVideoPlayer(
    selectedMedia?.type === 'video' ? selectedMedia.uri : null,
    (player) => {
      player.loop = true;
      player.muted = false;
      player.play();
    }
  );

  // Update video player source when selectedMedia changes
  useEffect(() => {
    if (selectedMedia?.type === 'video' && selectedMedia.uri) {
      videoPlayer.replaceAsync(selectedMedia.uri);
    }
  }, [selectedMedia, videoPlayer]);

  // Aspect ratio options
  const aspectRatios = [
    { label: 'Story (9:16)', ratio: [9, 16] as [number, number] },
    { label: 'Square (1:1)', ratio: [1, 1] as [number, number] },
    { label: 'Landscape (16:9)', ratio: [16, 9] as [number, number] },
    { label: 'Portrait (4:5)', ratio: [4, 5] as [number, number] },
  ];

  const pickImage = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        Toast.show({
          type: 'error',
          text1: 'Permission Required',
          text2: 'Sorry, we need camera roll permissions to make this work!',
          position: 'bottom',
          theme: isDark ? 'dark' : 'light',
          backgroundColor: colors.background,
          autoHide: true,
        });
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images', 'videos'],
        allowsEditing: true,
        aspect: selectedAspectRatio,
        quality: 1,
        videoMaxDuration: 30, // 30 seconds max for stories
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setSelectedMedia(result.assets[0]);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to pick media',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    }
  };

  const openCamera = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();

      if (status !== 'granted') {
        Toast.show({
          type: 'error',
          text1: 'Permission Required',
          text2: 'Sorry, we need camera permissions to make this work!',
          position: 'bottom',
          theme: isDark ? 'dark' : 'light',
          backgroundColor: colors.background,
          autoHide: true,
        });
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ['images', 'videos'],
        allowsEditing: true,
        aspect: selectedAspectRatio,
        quality: 1,
        videoMaxDuration: 30, // 30 seconds max for stories
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setSelectedMedia(result.assets[0]);
      }
    } catch (error) {
      console.error('Error using camera:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to take photo/video',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    }
  };

  const editMedia = async () => {
    if (!selectedMedia) return;

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: [selectedMedia.type === 'video' ? 'videos' : 'images'],
        allowsEditing: true,
        aspect: selectedAspectRatio,
        quality: 1,
        videoMaxDuration: 30,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setSelectedMedia(result.assets[0]);
      }
    } catch (error) {
      console.error('Error editing media:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to edit media',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    }
  };

  const handlePost = async () => {
    if (!selectedMedia) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Please select an image or video for your story',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
      return;
    }

    if (!user?.id) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'User not found. Please try logging in again.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
      return;
    }

    setUploading(true);

    try {
      // Create a File object from the selected media
      const fileExtension = selectedMedia.uri.split('.').pop() || '';
      const mimeType =
        selectedMedia.type === 'video' ? `video/${fileExtension}` : `image/${fileExtension}`;

      const file = {
        uri: selectedMedia.uri,
        type: mimeType,
        name: `story_${Date.now()}.${fileExtension}`,
      } as any;

      // Upload the story using FileService
      const response = await FileService.createStory([file], user.id.toString());

      if (response.success) {
        setSelectedMedia(null); // Clear the selected media
        Toast.show({
          type: 'success',
          text1: 'Success',
          text2: 'Story uploaded successfully!',
          position: 'bottom',
          theme: isDark ? 'dark' : 'light',
          backgroundColor: colors.background,
          autoHide: true,
        });
        navigation.goBack();
      } else {
        throw new Error(response.message || 'Failed to upload story');
      }
    } catch (error) {
      console.error('Error uploading story:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to upload story. Please try again.',
        position: 'bottom',
        theme: isDark ? 'dark' : 'light',
        backgroundColor: colors.background,
        autoHide: true,
      });
    } finally {
      setUploading(false);
    }
  };

  return (
    <View className={`flex-1 `} style={{ backgroundColor: colors.background }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      <View className="flex-row items-center justify-between px-4 pb-4 pt-12">
        <TouchableOpacity className="p-2" onPress={() => navigation.goBack()}>
          <Ionicons name="close" size={28} color={isDark ? '#fff' : '#000'} />
        </TouchableOpacity>
        <Text className={`font-medium text-lg ${isDark ? 'text-white' : 'text-black'}`}>
          Create Story
        </Text>
        <TouchableOpacity
          className={`rounded-full bg-violet-600 px-4 py-2 ${!selectedMedia || uploading ? 'opacity-60' : ''}`}
          onPress={handlePost}
          disabled={!selectedMedia || uploading}>
          {uploading ? (
            <View className="flex-row items-center">
              <ActivityIndicator size="small" color="#fff" />
              <Text className="ml-2 font-medium text-white">Uploading...</Text>
            </View>
          ) : (
            <Text className="font-medium text-white">Post</Text>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView
        className="flex-1"
        contentContainerStyle={{ flexGrow: 1, padding: 16 }}
        keyboardShouldPersistTaps="handled">
        {selectedMedia ? (
          <View
            className="relative flex-1 overflow-hidden rounded-xl"
            style={{ height: SCREEN_WIDTH * 1.8 }}>
            {selectedMedia.type === 'video' ? (
              <VideoView
                player={videoPlayer}
                style={{ height: '100%', width: '100%' }}
                contentFit="cover"
                nativeControls={false}
              />
            ) : (
              <Image
                source={{ uri: selectedMedia.uri }}
                className="h-full w-full"
                resizeMode="contain"
              />
            )}

            {/*  <View className="absolute left-0 right-0 px-4 bottom-20">
              <TextInput
                style={{
                  color: textColor,
                  textShadowColor: 'rgba(0,0,0,0.8)',
                  textShadowOffset: { width: 1, height: 1 },
                  textShadowRadius: 3,
                  fontSize: 18,
                  fontWeight: '600',
                }}
                placeholder="Add a caption..."
                placeholderTextColor="rgba(255,255,255,0.8)"
                value={caption}
                onChangeText={setCaption}
                multiline
                maxLength={100}
              />
            </View>
 */}
            {/*  <View className="absolute left-0 right-0 bottom-4">
              <ScrollView horizontal showsHorizontalScrollIndicator={false} className="px-4">
                {colorOptions.map((color) => (
                  <TouchableOpacity
                    key={color}
                    className={`mr-3 h-[30px] w-[30px] rounded-full border border-white/50 ${textColor === color ? 'border-[3px] border-violet-600' : ''}`}
                    style={{ backgroundColor: color }}
                    onPress={() => setTextColor(color)}
                  />
                ))}
              </ScrollView>
            </View> */}

            <TouchableOpacity
              className="absolute right-4 top-4 h-10 w-10 items-center justify-center rounded-full bg-black/60"
              onPress={() => setShowAspectRatios(!showAspectRatios)}>
              <MaterialIcons name="aspect-ratio" size={20} color="#fff" />
            </TouchableOpacity>

            <TouchableOpacity
              className="absolute right-4 top-16 h-10 w-10 items-center justify-center rounded-full bg-black/60"
              onPress={editMedia}>
              <MaterialIcons name="edit" size={20} color="#fff" />
            </TouchableOpacity>

            {showAspectRatios && (
              <View className="absolute left-4 right-4 top-4 rounded-lg bg-black/80 p-3">
                <Text className="mb-3 font-medium text-white">Choose Aspect Ratio</Text>
                {aspectRatios.map((item, index) => (
                  <TouchableOpacity
                    key={index}
                    className={`mb-2 rounded-lg p-3 ${
                      selectedAspectRatio[0] === item.ratio[0] &&
                      selectedAspectRatio[1] === item.ratio[1]
                        ? 'bg-violet-600'
                        : 'bg-gray-700'
                    }`}
                    onPress={() => {
                      setSelectedAspectRatio(item.ratio);
                      setShowAspectRatios(false);
                    }}>
                    <Text className="font-medium text-white">{item.label}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>
        ) : (
          <View className="flex-1 items-center justify-center px-6">
            <Text
              className={`mb-4 text-center font-bold text-2xl ${isDark ? 'text-white' : 'text-black'}`}>
              Create New Story
            </Text>
            <Text
              className={`mb-10 text-center text-base ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
              Share a photo or video that will disappear after 24 hours
            </Text>

            <View className="w-full flex-row justify-around">
              <TouchableOpacity
                className={`h-[120px] w-[120px] items-center justify-center rounded-xl `}
                style={{ backgroundColor: colors.grey5 }}
                onPress={pickImage}>
                <MaterialCommunityIcons name="image-multiple" size={32} color="#5A4FCF" />
                <Text
                  className={`mt-3 font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}>
                  Gallery
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                className={`h-[120px] w-[120px] items-center justify-center rounded-xl `}
                style={{ backgroundColor: colors.grey5 }}
                onPress={openCamera}>
                <Ionicons name="camera" size={32} color="#5A4FCF" />
                <Text
                  className={`mt-3 font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}>
                  Camera
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </ScrollView>
    </View>
  );
}
