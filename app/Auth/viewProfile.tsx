import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image, ActivityIndicator } from 'react-native';
import { Toast } from 'toastify-react-native';

import PhotoViewModal from '~/components/Profile/PhotoViewModal';
import { useColorScheme } from '~/lib/useColorScheme';
import { EventService } from '~/services/EventService';
import { FileService } from '~/services/FileService';
import { UserService } from '~/services/UserService';

interface ProfilePhoto {
  id: string;
  secureUrl: string;
  publicId?: string;
}

interface UserProfile {
  id: string;
  fullName: string;
  username: string;
  email?: string;
  bio: string;
  profilePicture: ProfilePhoto[];
  profileUploads: ProfilePhoto[];
  interests: string[];
  eventPreferences: string[];
  gender: string;
  upFor?: string;
  stats?: {
    events: number;
    followers: number;
    following: number;
  };
  isOnline?: boolean;
  lastActive?: string;
  location?: string;
}

export default function ViewProfileScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const userEmail = params.email as string;
  const userId = params.userId as string;
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [hasStory, setHasStory] = useState(false);
  const [isFriend, setIsFriend] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedPhoto, setSelectedPhoto] = useState<ProfilePhoto | null>(null);
  const [isPhotoModalVisible, setIsPhotoModalVisible] = useState(false);

  // State for events
  const [createdEvents, setCreatedEvents] = useState<any[]>([]);
  const [isLoadingCreatedEvents, setIsLoadingCreatedEvents] = useState(false);
  const [savedEvents, setSavedEvents] = useState<string[]>([]);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (userEmail) {
      fetchUserProfile();
      checkUserStories();
    }
  }, [userEmail]);

  // Fetch created events when the component mounts or when user profile is loaded
  useEffect(() => {
    const fetchCreatedEvents = async () => {
      if (userProfile?.id && createdEvents.length === 0) {
        setIsLoadingCreatedEvents(true);
        try {
          const response = await EventService.getEvents({ userId: userProfile.id });
          if (response.success && response.body) {
            setCreatedEvents(response.body);
          }
        } catch (error) {
          console.error('Error fetching created events:', error);
          setCreatedEvents([]);
        } finally {
          setIsLoadingCreatedEvents(false);
        }
      }
    };

    fetchCreatedEvents();
  }, [userProfile?.id]);

  // Fetch saved events to check if events are saved
  useEffect(() => {
    const fetchSavedEvents = async () => {
      if (userProfile?.id) {
        try {
          const response = await EventService.getSavedEvents(userProfile.id);
          if (response.success && response.body) {
            const eventIds = response.body.events.map((event: any) =>
              (event.id || event.eventId || event._id).toString()
            );
            setSavedEvents(eventIds);
          }
        } catch (error) {
          console.error('Error fetching saved events:', error);
        }
      }
    };

    fetchSavedEvents();
  }, [userProfile?.id]);

  // Filter events based on created events only
  const filteredEvents = createdEvents.map((event) => ({
    ...event,
    name: event.title,
    date: event.startDateTime ? format(new Date(event.startDateTime), 'MMMM d, yyyy') : '',
    image:
      event.coverImages && event.coverImages.length > 0
        ? event.coverImages[event.coverImages.length - 1].secureUrl
        : 'https://images.unsplash.com/photo-1540575467063-178a50c2df87',
    attending: event.attendees?.length || 0,
    created: true,
    ticketsSold: event.attendees?.length || 0,
    revenue:
      event.ticketTypes?.reduce((total: number, ticket: any) => {
        const soldTickets =
          event.attendees?.filter((attendee: any) => attendee.ticketType === ticket.id).length || 0;
        return total + soldTickets * ticket.price;
      }, 0) || 0,
    isSaved: savedEvents.includes(event.id.toString()),
  }));

  const fetchUserProfile = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('fetch users');
      const response = await UserService.getOtherUser({ email: userEmail });
      console.log(response);
      console.log(userEmail);
      if (response.success && response.body) {
        setUserProfile(response.body);
        // Mock friendship status - in a real app this would come from the response
        setIsFriend(Math.random() > 0.5); // Random for demo
      } else {
        setError('Failed to load user profile');
      }
    } catch (error: any) {
      console.error('Error fetching user profile:', error);
      setError(error.message || 'Failed to load user profile');
    } finally {
      setLoading(false);
    }
  };

  const checkUserStories = async () => {
    try {
      const response = await FileService.getUserStories(userId);
      if (response.success && response.body && response.body.length > 0) {
        const activeStories = response.body.some(
          (story: any) => story.storyImages && story.storyImages.length > 0
        );
        setHasStory(activeStories);
      }
    } catch (error) {
      console.error('Error checking user stories:', error);
      setHasStory(false);
    }
  };

  // Handle save/unsave event
  const handleSaveEvent = async (eventId: string, isSaved: boolean) => {
    if (!userProfile?.id) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Unable to save event. Please try again.',
        position: 'bottom',
      });
      return;
    }

    setIsSaving(true);
    try {
      await EventService.saveEvent(userProfile.id, eventId);

      // Update local saved events state
      if (isSaved) {
        // Remove from saved events
        setSavedEvents((prev) => prev.filter((id) => id !== eventId));
      } else {
        // Add to saved events
        setSavedEvents((prev) => [...prev, eventId]);
      }

      Toast.show({
        type: 'success',
        text1: isSaved ? 'Event Unsaved' : 'Event Saved',
        text2: isSaved
          ? 'Event removed from your saved events'
          : 'Event added to your saved events',
        position: 'bottom',
      });
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: error.message || 'Failed to save event',
        position: 'bottom',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const formatTimeAgo = (dateString: string) => {
    if (!dateString) return 'Offline';

    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return `${diffInSeconds}s ago`;
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}m ago`;
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    } else {
      return `${Math.floor(diffInSeconds / 86400)}d ago`;
    }
  };

  const handleViewStory = () => {
    if (hasStory) {
      router.push({
        pathname: '/story/view_other',
        params: { userId: userId.toString() },
      });
    }
  };

  const handleChat = () => {
    router.push({
      pathname: '/chat',
      params: { userId: userId.toString(), name: userProfile?.fullName },
    });
  };

  const handleAddFriend = () => {
    // Mock adding friend
    setIsFriend(true);
    Toast.show({
      type: 'success',
      text1: 'Friend Request Sent',
      text2: `Friend request sent to ${userProfile?.fullName}`,
      position: 'bottom',
      theme: isDark ? 'dark' : 'light',
      backgroundColor: colors.background,
      autoHide: true,
    });
  };

  const handlePhotoPress = (photo: ProfilePhoto) => {
    setSelectedPhoto(photo);
    setIsPhotoModalVisible(true);
  };

  const handleClosePhotoModal = () => {
    setIsPhotoModalVisible(false);
    setSelectedPhoto(null);
  };

  const renderProfilePhotos = () => {
    const photos = userProfile?.profileUploads || [];
    if (photos.length === 0) return null;

    return (
      <View className="mb-6 px-4">
        <Text className={`mb-3 text-lg font-semibold ${isDark ? 'text-white' : 'text-black'}`}>
          Photos
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {photos.map((photo, index) => (
            <TouchableOpacity
              key={photo.id}
              className="mr-2 overflow-hidden rounded-lg"
              onPress={() => handlePhotoPress(photo)}>
              <Image
                source={{ uri: photo.secureUrl }}
                className="h-20 w-20 rounded-lg"
                resizeMode="cover"
              />
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  if (loading) {
    return (
      <View
        className="flex-1 items-center justify-center"
        style={{ backgroundColor: colors.background }}>
        <StatusBar style={isDark ? 'light' : 'dark'} />
        <ActivityIndicator size="large" color={colors.primary} />
        <Text className={`mt-4 text-base ${isDark ? 'text-white' : 'text-black'}`}>
          Loading profile...
        </Text>
      </View>
    );
  }

  if (error || !userProfile) {
    return (
      <View
        className="flex-1 items-center justify-center px-4"
        style={{ backgroundColor: colors.background }}>
        <StatusBar style={isDark ? 'light' : 'dark'} />
        <MaterialCommunityIcons name="account-alert" size={64} color={isDark ? '#666' : '#ccc'} />
        <Text className={`mt-4 text-lg font-semibold ${isDark ? 'text-white' : 'text-black'}`}>
          Unable to Load Profile
        </Text>
        <Text
          className={`mt-2 text-center text-base ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
          {error || 'This profile could not be found or is not available.'}
        </Text>
        <TouchableOpacity
          className="mt-6 rounded-full bg-violet-600 px-6 py-3"
          onPress={() => router.back()}>
          <Text className="font-medium text-white">Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View className="flex-1" style={{ backgroundColor: colors.background }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      {/* Header */}
      <View className="flex-row items-center justify-between px-4 pb-2 pt-14">
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={isDark ? '#fff' : '#000'} />
        </TouchableOpacity>
        <Text className={`text-xl font-semibold ${isDark ? 'text-white' : 'text-black'}`}>
          {userProfile.fullName}'s Profile
        </Text>
        <View className="w-6" />
      </View>

      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 40 }}>
        {/* Cover and Profile Section */}
        <View className="relative">
          {/* Cover Image */}
          <View className="h-32 bg-violet-600/20" />

          {/* Profile Image */}
          <View className="absolute left-4 top-16 items-center">
            <View className={`rounded-full p-1 ${hasStory ? 'bg-violet-600' : 'bg-transparent'}`}>
              <Image
                source={{
                  uri:
                    userProfile.profilePicture && userProfile.profilePicture.length > 0
                      ? userProfile.profilePicture[userProfile.profilePicture.length - 1].secureUrl
                      : 'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
                }}
                className="h-24 w-24 rounded-full border-2 border-white"
              />
            </View>
          </View>

          {/* Action Buttons */}
          <View className="mt-12 flex-row justify-end p-4">
            {hasStory && (
              <TouchableOpacity
                className="mr-3 h-10 w-10 items-center justify-center rounded-full bg-violet-600"
                onPress={handleViewStory}>
                <Ionicons name="play" size={18} color="#fff" />
              </TouchableOpacity>
            )}

            <TouchableOpacity
              className="mr-3 h-10 w-10 items-center justify-center rounded-full"
              style={{ backgroundColor: colors.grey5 }}
              onPress={handleChat}>
              <Ionicons name="chatbubble-outline" size={18} color={colors.foreground} />
            </TouchableOpacity>

            {!isFriend ? (
              <TouchableOpacity
                className="h-10 flex-row items-center justify-center rounded-full bg-violet-600 px-4"
                onPress={handleAddFriend}>
                <Ionicons name="person-add-outline" size={16} color="#fff" className="mr-1" />
                <Text className="ml-1 font-medium text-white">Add Friend</Text>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                className="h-10 flex-row items-center justify-center rounded-full px-4"
                style={{ backgroundColor: colors.grey5 }}>
                <Ionicons name="checkmark" size={16} color={colors.foreground} className="mr-1" />
                <Text style={{ color: colors.foreground }} className="ml-1 font-medium">
                  Friends
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Profile Info */}
        <View className="mt-2 px-4">
          <Text className={`font-bold text-2xl ${isDark ? 'text-white' : 'text-black'}`}>
            {userProfile.fullName}
          </Text>

          <Text className={`text-base ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
            @{userProfile.username}
          </Text>

          <View className="mb-3 mt-1 flex-row items-center">
            <View
              className={`mr-2 h-2.5 w-2.5 rounded-full ${userProfile.isOnline ? 'bg-green-500' : 'bg-gray-400'}`}
            />
            <Text className={`${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
              {userProfile.isOnline ? 'Online' : formatTimeAgo(userProfile.lastActive || '')}
            </Text>
          </View>

          {userProfile.bio && (
            <Text className={`mb-4 text-base ${isDark ? 'text-white' : 'text-black'}`}>
              {userProfile.bio}
            </Text>
          )}

          {/* Stats */}
          {userProfile.stats && (
            <View className="mb-6 w-full flex-row justify-around py-4">
              <View className="items-center">
                <Text className={`font-bold text-xl ${isDark ? 'text-white' : 'text-black'}`}>
                  {userProfile.stats.events || 0}
                </Text>
                <Text className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                  Events
                </Text>
              </View>

              <View className="items-center">
                <Text className={`font-bold text-xl ${isDark ? 'text-white' : 'text-black'}`}>
                  {userProfile.stats.followers || 0}
                </Text>
                <Text className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                  Followers
                </Text>
              </View>

              <View className="items-center">
                <Text className={`font-bold text-xl ${isDark ? 'text-white' : 'text-black'}`}>
                  {userProfile.stats.following || 0}
                </Text>
                <Text className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                  Following
                </Text>
              </View>
            </View>
          )}
        </View>

        {/* Profile Photos */}
        {renderProfilePhotos()}

        {/* Interests */}
        {userProfile.interests && userProfile.interests.length > 0 && (
          <View className="mb-6 px-4">
            <Text className={`mb-2 font-bold text-lg ${isDark ? 'text-white' : 'text-black'}`}>
              Interests
            </Text>
            <View className="flex-row flex-wrap">
              {userProfile.interests.map((interest, index) => (
                <View
                  key={index}
                  className="mb-2 mr-2 rounded-full px-3 py-1.5"
                  style={{ backgroundColor: colors.grey5 }}>
                  <Text className="font-medium text-sm" style={{ color: colors.foreground }}>
                    {interest}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Event Preferences */}
        {userProfile.eventPreferences && userProfile.eventPreferences.length > 0 && (
          <View className="mb-6 px-4">
            <Text className={`mb-2 font-bold text-lg ${isDark ? 'text-white' : 'text-black'}`}>
              Event Preferences
            </Text>
            <View className="flex-row flex-wrap">
              {userProfile.eventPreferences.map((preference, index) => (
                <View
                  key={index}
                  className="mb-2 mr-2 rounded-full px-3 py-1.5"
                  style={{ backgroundColor: colors.grey5 }}>
                  <Text className="font-medium text-sm" style={{ color: colors.foreground }}>
                    {preference}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Up For */}
        {userProfile.upFor && (
          <View className="mb-6 px-4">
            <Text className={`mb-2 font-bold text-lg ${isDark ? 'text-white' : 'text-black'}`}>
              Up For
            </Text>
            <View className="rounded-lg px-3 py-2" style={{ backgroundColor: colors.grey5 }}>
              <Text className="text-base" style={{ color: colors.foreground }}>
                {userProfile.upFor}
              </Text>
            </View>
          </View>
        )}

        {/* Created Events Section */}
        <View className="mb-4 px-4">
          <Text className={`mb-3 font-bold text-lg ${isDark ? 'text-white' : 'text-black'}`}>
            Created Events
          </Text>
        </View>

        {/* Events List */}
        <View className="px-4">
          {isLoadingCreatedEvents ? (
            <View className="items-center justify-center py-10">
              <Text className={`text-base ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                Loading created events...
              </Text>
            </View>
          ) : filteredEvents.length > 0 ? (
            filteredEvents.map((event) => (
              <TouchableOpacity
                key={event.id}
                className="mb-4 overflow-hidden rounded-xl shadow-sm"
                style={{ backgroundColor: colors.grey5 }}
                onPress={() => {
                  router.push({
                    pathname: '/Events/viewEvent',
                    params: { eventId: event.id },
                  });
                }}>
                <View className="flex-row">
                  <Image source={{ uri: event.image }} className="h-full w-[100px]" />
                  <View className="flex-1 justify-center p-3">
                    <View className="mb-2 flex-row items-center justify-between">
                      <Text
                        className={`font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}>
                        {event.name}
                      </Text>
                      <TouchableOpacity
                        onPress={() => handleSaveEvent(event.id.toString(), event.isSaved)}
                        disabled={isSaving}
                        className="p-1">
                        <Ionicons
                          name={event.isSaved ? 'bookmark' : 'bookmark-outline'}
                          size={20}
                          color={event.isSaved ? colors.primary : colors.grey}
                        />
                      </TouchableOpacity>
                    </View>
                    <View className="mb-1 flex-row items-center">
                      <Ionicons name="calendar" size={16} color={isDark ? '#bbb' : '#666'} />
                      <Text
                        className={`ml-1.5 text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                        {event.date}
                      </Text>
                    </View>
                    <View className="flex-row items-center">
                      <Ionicons name="people" size={16} color={isDark ? '#bbb' : '#666'} />
                      <Text
                        className={`ml-1.5 text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                        {event.attending} attending
                      </Text>
                    </View>
                  </View>
                </View>
              </TouchableOpacity>
            ))
          ) : (
            <View className="items-center justify-center py-10">
              <MaterialCommunityIcons
                name="calendar-plus"
                size={60}
                color={isDark ? '#444' : '#ddd'}
              />
              <Text
                className={`mb-5 mt-4 text-center text-base ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                {`${userProfile?.fullName || 'This user'} hasn't created any events yet`}
              </Text>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Photo View Modal */}
      <PhotoViewModal
        visible={isPhotoModalVisible}
        photo={selectedPhoto}
        onClose={handleClosePhotoModal}
      />
    </View>
  );
}
