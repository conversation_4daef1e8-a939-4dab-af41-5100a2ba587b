import React, {
  createContext,
  PropsWithChildren,
  RefObject,
  useContext,
  useRef,
  useCallback,
  useMemo,
} from 'react';

import { PersonProfileSheetHandle } from '~/components/People/PersonProfileSheet';
import staticPeople from '~/data/people.json';
import { EventService } from '~/services/EventService';
import { UserService } from '~/services/UserService';
import { UserStore } from '~/store/store';
import {
  EventType,
  EventContextType,
  coodinates,
  ViewModeType,
  ViewModeMap,
  MapTsType,
  MapTsTypeMap,
} from '~/types';
import { Person } from '~/types/people_type';

const EventProviderContext = createContext<EventContextType>({
  selectedEvent: null,
  setSelectedEvent: () => {},
  filteredEvents: [],
  setSearchQuery: () => {},
  categories: [],
  searchQuery: 'All',
  cameraRef: null,
  zoomToLocation: () => {},
  resetToUserLocation: () => {},
  followUserLocation: true,
  setFollowUserLocation: () => {},
  ViewMode: ViewModeMap.ListView,
  setViewMode: () => {},
  MapType: MapTsTypeMap.Events,
  setMapType: () => {},
  People: [],
  zoomLevel: 14,
  setZoomLevel: () => {},
  userId: null,
  setUserId: () => {},
  personProfileSheetRef: { current: null } as unknown as RefObject<PersonProfileSheetHandle>,
  setPeople: () => {},
  isPersonProfileSheetOpen: false,
  setIsPersonProfileSheetOpen: () => {},
  isLoadingEvents: false,
  isLoadingCategories: false,
  isLoadingPeople: false,
  eventsError: null,
  categoriesError: null,
  peopleError: null,
  fetchEvents: async () => [],
  fetchCategories: async () => [],
  fetchPeople: async () => [],
});

// Custom hook to selectively access context values
// This helps prevent unnecessary rerenders when only specific values are needed
export const useEventValue = <K extends keyof EventContextType>(key: K): EventContextType[K] => {
  const context = useContext(EventProviderContext);
  return context[key];
};

export const EventProvider = ({ children }: PropsWithChildren) => {
  const [selectedEvent, setSelectedEvent] = React.useState<EventType | null>(null);
  const [People, setPeople] = React.useState<Person[]>(staticPeople as Person[]);
  const [filteredEvents, setFilteredEvents] = React.useState<EventType[]>([]);
  const [allEvents, setAllEvents] = React.useState<EventType[]>([]);
  const [searchQuery, setSearchQuery] = React.useState<string>('All');
  const [categories, setCategories] = React.useState<string[]>([]);
  const [ViewMode, setViewMode] = React.useState<ViewModeType>(ViewModeMap.ListView);
  const [MapType, setMapType] = React.useState<MapTsType>(MapTsTypeMap.People);
  const [isLoadingEvents, setIsLoadingEvents] = React.useState<boolean>(false);
  const [isLoadingCategories, setIsLoadingCategories] = React.useState<boolean>(false);
  const [isLoadingPeople, setIsLoadingPeople] = React.useState<boolean>(false);
  const [eventsError, setEventsError] = React.useState<string | null>(null);
  const [categoriesError, setCategoriesError] = React.useState<string | null>(null);
  const [peopleError, setPeopleError] = React.useState<string | null>(null);
  const cameraRef = useRef<any>(null);
  const [followUserLocation, setFollowUserLocation] = React.useState(true);
  const [zoomLevel, setZoomLevel] = React.useState<number>(14);
  const [userId, setUserId] = React.useState<string | number | null>(null);
  const personProfileSheetRef = useRef<PersonProfileSheetHandle>(null);
  const [isPersonProfileSheetOpen, setIsPersonProfileSheetOpen] = React.useState(false);

  // Fetch events from API
  const fetchEvents = useCallback(async (queryParams: any = {}) => {
    try {
      setIsLoadingEvents(true);
      setEventsError(null);
      const response = await EventService.getEvents(queryParams);

      // Handle the specific API response structure: { body: [...], message: "...", success: true }
      let events: EventType[] = [];
      if (response && Array.isArray(response.body)) {
        // Main case: response.body contains the events array
        events = response.body.map((event: any) => ({
          id: event.id,
          title: event.title,
          user: event.user,
          description: event.description,
          location: event.location,
          locationData: event.locationData,
          eventUploads: event.eventUploads,
          storyImages: event.storyImages,
          coverImage:
            event.coverImages && event.coverImages.length > 0
              ? event.coverImages[0].secureUrl || event.coverImages[0].url || null
              : null,
          startDateTime: event.startDateTime,
          endDateTime: event.endDateTime,
          eventType: event.eventCategory?.name || event.eventCategory?.categoryName || 'General',
          visibility: event.visibility,
          isPaid: event.paid,
          ticketSetup: event.ticketSetup,
          owners: event.owners || [],
          currency: event.currency || 'USD',
        }));
      } else if (Array.isArray(response)) {
        // Fallback: direct array response
        events = response;
      } else if (response && Array.isArray(response.data)) {
        // Fallback: response.data contains events
        events = response.data;
      } else if (response && Array.isArray(response.events)) {
        // Fallback: response.events contains events
        events = response.events;
      } else {
        console.warn('Unexpected API response structure:', response);
        events = [];
      }

      setAllEvents(events);
      return events;
    } catch (error) {
      console.error('Error fetching events:', error);
      setEventsError(error instanceof Error ? error.message : 'Failed to fetch events');
      // Set empty array on error to prevent undefined issues
      setAllEvents([]);
      return [];
    } finally {
      setIsLoadingEvents(false);
    }
  }, []);

  // Fetch event categories from API
  const fetchCategories = useCallback(async () => {
    try {
      setIsLoadingCategories(true);
      setCategoriesError(null);
      const response = await EventService.getEventCategories();

      // Handle the specific API response structure: { body: [...], message: "...", success: true }
      let categoryNames: string[] = [];

      if (response && Array.isArray(response.body)) {
        // Main case: response.body contains the categories array
        categoryNames = response.body.map((cat: any) => cat.categoryName || cat.name || cat);
      } else if (Array.isArray(response.data)) {
        // Fallback: response.data contains categories
        categoryNames = response.data.map((cat: any) => cat.categoryName || cat.name || cat);
      } else if (Array.isArray(response.categories)) {
        // Fallback: response.categories contains categories
        categoryNames = response.categories.map((cat: any) => cat.categoryName || cat.name || cat);
      } else if (Array.isArray(response)) {
        // Fallback: direct array response
        categoryNames = response.map((cat: any) => cat.categoryName || cat.name || cat);
      } else {
        console.warn('Unexpected categories API response structure:', response);
        categoryNames = [];
      }

      // Ensure we always have 'All' as the first option
      const allCategories = ['All', ...categoryNames];
      setCategories(allCategories);
      return allCategories;
    } catch (error) {
      console.error('Error fetching categories:', error);
      setCategoriesError(error instanceof Error ? error.message : 'Failed to fetch categories');
      // Fallback to 'All' if API fails
      setCategories(['All']);
      return ['All'];
    } finally {
      setIsLoadingCategories(false);
    }
  }, []);

  // Fetch people from API
  const fetchPeople = useCallback(async () => {
    try {
      setIsLoadingPeople(true);
      setPeopleError(null);

      const response = await UserService.getUsers();

      // Get current user from store to exclude them
      const userStore = UserStore.getState() as { user: any };
      const currentUser = userStore.user;
      const currentUserId = currentUser?.id;

      // Handle the API response structure
      let users: Person[] = [];
      let rawUsers: any[] = [];

      if (response && Array.isArray(response.body)) {
        rawUsers = response.body;
      } else if (Array.isArray(response.data)) {
        rawUsers = response.data;
      } else if (Array.isArray(response)) {
        rawUsers = response;
      } else {
        console.warn('⚠️ Unexpected users API response structure:', response);
        rawUsers = [];
      }

      // Transform API users to Person format and filter out current user and users without location
      users = rawUsers
        .filter((user: any) => {
          // Exclude current user
          if (currentUserId && user.id === currentUserId) {
            return false;
          }
          // Only include users with valid location
          const hasValidLocation =
            user.location && Array.isArray(user.location) && user.location.length >= 2;
          if (!hasValidLocation) {
            console.log('📍 User without valid location:', user.fullName, user.location);
          }
          return hasValidLocation;
        })
        .map((user: any) => {
          // Get the profile picture from profilePicture array (use the latest one)
          let profilePhoto =
            'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png';
          if (
            user.profilePicture &&
            Array.isArray(user.profilePicture) &&
            user.profilePicture.length > 0
          ) {
            // Use the most recent profile picture (last in array or highest version)
            const latestPicture = user.profilePicture.reduce((latest: any, current: any) => {
              return current.version > latest.version ? current : latest;
            });
            profilePhoto = latestPicture.secureUrl || latestPicture.url || profilePhoto;
          }

          return {
            id: user.id,
            name: user.fullName || 'Unknown User',
            age: user.age || 25, // Default age if not provided
            bio: user.bio || 'No bio available',
            profilePhoto,
            interests: user.interests || [],
            isOnline: user.isOnline || false,
            lastActive: user.lastActive || user.loginTime || new Date().toISOString(),
            upFor: user.upFor || '',
            location: user.location || '',
            // Use location coordinates from the location array
            long: user.location[0],
            lat: user.location[1],
            email: user.email,
          };
        });

      setPeople(users);
      return users;
    } catch (error) {
      console.error('❌ Error fetching people:', error);
      setPeopleError(error instanceof Error ? error.message : 'Failed to fetch people');
      // Keep existing static data on error instead of clearing it
      console.log('🔄 Keeping static people data due to API error');
      return staticPeople as Person[];
    } finally {
      setIsLoadingPeople(false);
    }
  }, []);

  // Initialize data on component mount
  React.useEffect(() => {
    const initializeData = async () => {
      await Promise.all([fetchEvents(), fetchCategories(), fetchPeople()]);
    };

    initializeData();
  }, [fetchEvents, fetchCategories, fetchPeople]);

  // Memoize callback functions to prevent unnecessary rerenders
  const zoomToLocation = useCallback((coordinates: coodinates, zoomLevel = 18, duration = 1000) => {
    setFollowUserLocation(false);
    if (cameraRef.current && cameraRef.current.setCamera) {
      cameraRef.current.setCamera({
        centerCoordinate: [coordinates.longitude, coordinates.latitude],
        zoomLevel,
        animationDuration: duration,
      });
    }
  }, []);

  const resetToUserLocation = useCallback(() => {
    setFollowUserLocation(true);
  }, []);

  // Memoize state setters
  const memoizedSetSelectedEvent = useCallback((event: EventType | null) => {
    setSelectedEvent(event);
  }, []);

  const memoizedSetSearchQuery = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  const memoizedSetViewMode = useCallback((mode: ViewModeType) => {
    setViewMode(mode);
  }, []);

  const memoizedSetMapType = useCallback((type: MapTsType) => {
    setMapType(type);
  }, []);

  const memoizedSetFollowUserLocation = useCallback((value: boolean) => {
    setFollowUserLocation(value);
  }, []);

  const memoizedSetZoomLevel = useCallback((zoom: number) => {
    setZoomLevel(zoom);
  }, []);

  const memoizedSetUserId = useCallback((id: string | number | null) => {
    setUserId(id);
  }, []);

  const memoizedSetPeople = useCallback((newPeople: Person[]) => {
    setPeople(newPeople);
  }, []);

  const memoizedSetIsPersonProfileSheetOpen = useCallback((isOpen: boolean) => {
    setIsPersonProfileSheetOpen(isOpen);
  }, []);

  React.useEffect(() => {
    if (searchQuery === 'All') {
      setFilteredEvents(allEvents);
      resetToUserLocation();
    } else {
      const filtered = allEvents.filter((event) => event.eventType === searchQuery);

      setFilteredEvents(filtered);
      setTimeout(() => {
        if (ViewMode === ViewModeMap.ScrollView && filtered.length > 0) {
          const coords = {
            latitude: filtered[0].locationData.coordinates[1],
            longitude: filtered[0].locationData.coordinates[0],
          };
          zoomToLocation(coords);
        }
      }, 500);
    }
  }, [searchQuery, ViewMode, allEvents, zoomToLocation, resetToUserLocation]);

  // Memoize the context value to prevent unnecessary rerenders
  const contextValue = useMemo(
    () => ({
      selectedEvent,
      setSelectedEvent: memoizedSetSelectedEvent,
      setSearchQuery: memoizedSetSearchQuery,
      filteredEvents,
      categories,
      searchQuery,
      cameraRef,
      zoomToLocation,
      resetToUserLocation,
      followUserLocation,
      setFollowUserLocation: memoizedSetFollowUserLocation,
      ViewMode,
      setViewMode: memoizedSetViewMode,
      MapType,
      setMapType: memoizedSetMapType,
      People,
      zoomLevel,
      setZoomLevel: memoizedSetZoomLevel,
      userId,
      setUserId: memoizedSetUserId,
      personProfileSheetRef,
      setPeople: memoizedSetPeople,
      isPersonProfileSheetOpen,
      setIsPersonProfileSheetOpen: memoizedSetIsPersonProfileSheetOpen,
      // Add new API-related values
      isLoadingEvents,
      isLoadingCategories,
      isLoadingPeople,
      eventsError,
      categoriesError,
      peopleError,
      fetchEvents,
      fetchCategories,
      fetchPeople,
    }),
    [
      selectedEvent,
      filteredEvents,
      categories,
      searchQuery,
      followUserLocation,
      ViewMode,
      MapType,
      People,
      zoomLevel,
      userId,
      isPersonProfileSheetOpen,
      isLoadingEvents,
      isLoadingCategories,
      isLoadingPeople,
      eventsError,
      categoriesError,
      peopleError,
      // Remove memoized functions from dependencies since they're stable with useCallback
      // memoizedSetSelectedEvent,
      // memoizedSetSearchQuery,
      // zoomToLocation,
      // resetToUserLocation,
      // memoizedSetFollowUserLocation,
      // memoizedSetViewMode,
      // memoizedSetMapType,
      // memoizedSetZoomLevel,
      // memoizedSetUserId,
      // memoizedSetPeople,
      // memoizedSetIsPersonProfileSheetOpen,
      // fetchEvents,
      // fetchCategories,
      // fetchPeople,
    ]
  );

  return (
    <EventProviderContext.Provider value={contextValue}>{children}</EventProviderContext.Provider>
  );
};

export const useEvent = () => useContext(EventProviderContext);
