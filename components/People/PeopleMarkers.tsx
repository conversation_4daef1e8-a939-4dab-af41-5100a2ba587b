import { MarkerView } from '@rnmapbox/maps';
import { memo, useMemo, useCallback } from 'react';
import { Image, View, Text, Pressable } from 'react-native';
import Animated from 'react-native-reanimated';

import { defaultProfileImage } from '~/assets/Pins';
import { useEvent, useEventValue } from '~/providers/MapProvider';
import { Person } from '~/types/people_type';

// Memoized Marker Component to avoid unnecessary re-renders
const PersonMarker = memo(
  ({
    person,
    zoomLevel,
    peopleWithStories,
    onPress,
  }: {
    person: Person;
    zoomLevel: number;
    peopleWithStories: string[];
    onPress: (person: Person) => void;
  }) => {
    return (
      <MarkerView coordinate={[person.long, person.lat]} anchor={{ x: 0.5, y: 1 }}>
        <Pressable onPressIn={() => onPress(person)} className="items-center gap-1">
          {zoomLevel >= 13 && person.upFor && (
            <View className="min-w-[80px] rounded-full bg-purple-600 px-3 py-1 shadow-sm">
              <Text className="text-center font-medium text-xs text-white" numberOfLines={1}>
                {person.upFor}
              </Text>
            </View>
          )}
          <View className="h-16 w-10 items-center">
            <View className="relative items-center">
              <View className="absolute top-[36px] z-[1] h-0 w-0 rotate-180 border-b-[15px] border-l-[10px] border-r-[10px] border-b-[white] border-l-transparent border-r-transparent" />
              <View className="absolute bottom-[-14px] z-0 h-[3px] w-[5px] rounded-full bg-black/20" />
              <Animated.View
                className={`z-[2] h-[40px] w-[40px] items-center justify-center overflow-hidden rounded-full border-2 ${peopleWithStories.includes(person.id.toString()) ? 'border-violet-600' : 'border-[white]'} bg-white `}>
                <Image
                  source={{ uri: person.profilePhoto || defaultProfileImage }}
                  className="h-[40px] w-[40px] rounded-full"
                />
                {person.isOnline && (
                  <View className="absolute right-[-3px] top-[-3px] z-[3] h-[10px] w-[10px] rounded-full border-[1.5px] border-white bg-[#4CAF50]" />
                )}
              </Animated.View>
            </View>
          </View>
        </Pressable>
      </MarkerView>
    );
  },
  (prevProps, nextProps) =>
    prevProps.person.id.toString() === nextProps.person.id.toString() &&
    prevProps.zoomLevel === nextProps.zoomLevel
);

function PeopleMarkerPage() {
  // Use selective context values to prevent unnecessary rerenders
  const People = useEventValue('People');
  const zoomLevel = useEventValue('zoomLevel');
  const { setUserId } = useEvent();

  // Memoize static data
  const peopleWithStories = useMemo(() => ['2', '3', '7'], []); // IDs of people who have stories

  // Memoize handlers
  const handlePersonPress = useCallback((person: Person) => {
    setUserId(person.id);
  }, []);

  // Memoize the list of markers to prevent unnecessary recreations
  const markers = useMemo(() => {
    return People.map((person) => (
      <PersonMarker
        key={person.id}
        person={person}
        zoomLevel={zoomLevel}
        peopleWithStories={peopleWithStories}
        onPress={handlePersonPress}
      />
    ));
  }, [People, zoomLevel, peopleWithStories, handlePersonPress]);

  return <>{markers}</>;
}

// Wrap the component with memo to prevent unnecessary rerenders
export default memo(function PeopleMarker() {
  return <PeopleMarkerPage />;
});
