import { Ionicons } from '@expo/vector-icons';
import { BottomSheetView, BottomSheetBackdrop, BottomSheetModal } from '@gorhom/bottom-sheet';
import React, { useState, useCallback, useMemo, useRef, useEffect, memo } from 'react';
import {
  View,
  TouchableOpacity,
  FlatList,
  Image,
  ActivityIndicator,
  Platform,
  Alert,
} from 'react-native';

import { Text } from '~/components/nativewindui/Text';
import { Box } from '~/components/ui/box';
import { Button, ButtonText } from '~/components/ui/button';
import { Input, InputField, InputSlot } from '~/components/ui/input';
import { useColorScheme } from '~/lib/useColorScheme';
import { useEventValue } from '~/providers/MapProvider';
import { MapTsTypeMap } from '~/types';
import { EventType } from '~/types/event_type';
import { Person } from '~/types/people_type';

interface SearchBottomSheetProps {
  visible: boolean;
  onClose: () => void;
}

// Memoized Event Item component to prevent unnecessary rerenders
const EventItem = memo(
  ({
    item,
    onPress,
    colors,
  }: {
    item: EventType;
    onPress: (item: EventType) => void;
    colors: any;
  }) => {
    return (
      <TouchableOpacity
        onPress={() => onPress(item)}
        className="flex-row border-b p-4"
        style={{ borderBottomColor: colors.grey5 }}>
        <Image
          source={{
            uri:
              item.coverImage ||
              'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
          }}
          className="h-[60px] w-[60px] rounded-lg"
        />
        <View className="ml-3 flex-1 justify-center">
          <Text className="mb-1 text-base font-semibold" style={{ color: colors.foreground }}>
            {item.title}
          </Text>
          <Text className="mb-1.5 text-sm" style={{ color: colors.grey }} numberOfLines={2}>
            {item.description}
          </Text>
          <View className="flex-row items-center">
            <Ionicons name="location" size={12} color={colors.grey} />
            <Text className="ml-1 text-xs" style={{ color: colors.grey }}>
              {item.location}
            </Text>
            <View className="mx-2 h-1 w-1 rounded-full" style={{ backgroundColor: colors.grey }} />
            <Text className="text-xs" style={{ color: colors.grey }}>
              {new Date(item.startDateTime).toLocaleDateString()}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  }
);

// Memoized Person Item component to prevent unnecessary rerenders
const PersonItem = memo(
  ({
    item,
    onPress,
    onFriendRequest,
    colors,
    isDark,
  }: {
    item: Person;
    onPress: (item: Person) => void;
    onFriendRequest: (person: Person) => void;
    colors: any;
    isDark: boolean;
  }) => {
    return (
      <TouchableOpacity
        onPress={() => onPress(item)}
        className="flex-row border-b p-4"
        style={{ borderBottomColor: colors.grey5 }}>
        <Image
          source={{
            uri:
              item.profilePhoto ||
              'https://res.cloudinary.com/dil9qrpak/image/upload/v1748976473/user-circles-set_hrudqa.png',
          }}
          className="h-[60px] w-[60px] rounded-full"
        />
        <View className="ml-3 flex-1 justify-center">
          <Text className="mb-1 text-base font-semibold" style={{ color: colors.foreground }}>
            {item.name}
          </Text>
          <Text className="mb-1.5 text-sm" style={{ color: colors.grey }} numberOfLines={2}>
            {item.bio}
          </Text>
          <View className="mt-1 flex-row flex-wrap">
            {item.interests.slice(0, 3).map((interest, index) => (
              <View
                key={index}
                className="mb-1 mr-2 rounded-full px-2 py-0.5"
                style={{ backgroundColor: colors.grey5 }}>
                <Text className="text-xs" style={{ color: colors.grey }}>
                  {interest}
                </Text>
              </View>
            ))}
            {item.interests.length > 3 && (
              <Text className="text-xs" style={{ color: colors.grey }}>
                +{item.interests.length - 3} more
              </Text>
            )}
          </View>
        </View>
        <View className="items-center justify-center">
          <View
            className="mb-2 h-2.5 w-2.5 rounded-full"
            style={{ backgroundColor: item.isOnline ? '#4ade80' : colors.grey }}
          />
          <Button
            size="sm"
            variant="solid"
            className={`rounded-full px-3 py-1 ${isDark ? 'bg-violet-700' : 'bg-violet-600'}`}
            onPress={() => onFriendRequest(item)}>
            <ButtonText className="text-xs font-semibold text-white">Add</ButtonText>
          </Button>
        </View>
      </TouchableOpacity>
    );
  }
);

// Memoized Empty List component to prevent unnecessary rerenders
const EmptyListComponent = memo(
  ({ searchQuery, MapType, colors }: { searchQuery: string; MapType: string; colors: any }) => (
    <View className="flex-1 items-center justify-center pt-[100px]">
      <Ionicons name="search-outline" size={64} color={colors.grey} />
      <Text className="mt-4 text-base" style={{ color: colors.foreground }}>
        {searchQuery
          ? `No ${MapType === MapTsTypeMap.Events ? 'events' : 'people'} found`
          : `Search for ${MapType === MapTsTypeMap.Events ? 'events' : 'people'}`}
      </Text>
    </View>
  )
);

const SearchBottomSheet = ({ visible, onClose }: SearchBottomSheetProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const { colors, isDark } = useColorScheme();

  // Use selective context values to prevent unnecessary rerenders
  const MapType = useEventValue('MapType');
  const filteredEvents = useEventValue('filteredEvents');
  const People = useEventValue('People');
  const zoomToLocation = useEventValue('zoomToLocation');
  const setSelectedEvent = useEventValue('setSelectedEvent');

  // Ref for bottom sheet modal
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);

  // Define snap points - memoized to prevent recreation
  const snapPoints = useMemo(() => ['95%'], []);

  // Memoize backdrop component to prevent recreation on every render
  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop {...props} disappearsOnIndex={-1} appearsOnIndex={0} opacity={0.6} />
    ),
    []
  );

  useEffect(() => {
    if (visible) {
      bottomSheetModalRef.current?.present();
      setLoading(true);
      // Simulate API call delay
      const timer = setTimeout(() => {
        setLoading(false);
      }, 500);
      return () => clearTimeout(timer);
    } else {
      bottomSheetModalRef.current?.dismiss();
    }
  }, [visible]);

  const handleSheetChanges = useCallback(
    (index: number) => {
      if (index === -1) {
        onClose();
        setSearchQuery('');
      }
    },
    [onClose]
  );

  // Clear search query - memoized to prevent recreation
  const handleClearSearch = useCallback(() => {
    setSearchQuery('');
  }, []);

  // Filter events or people based on search query - memoized to prevent recalculation
  const filteredItems = useMemo(() => {
    const query = searchQuery.toLowerCase();
    if (MapType === MapTsTypeMap.Events) {
      // Add null check and fallback to empty array
      const events = filteredEvents || [];
      return events.filter(
        (event) =>
          event.title.toLowerCase().includes(query) ||
          event.description.toLowerCase().includes(query) ||
          event.location.toLowerCase().includes(query) ||
          event.eventType.toLowerCase().includes(query)
      );
    } else {
      // Add null check and fallback to empty array
      const people = People || [];
      return people.filter((person) => {
        // First check if person has valid location data
        const hasValidLocation =
          typeof person.lat === 'number' &&
          typeof person.long === 'number' &&
          !isNaN(person.lat) &&
          !isNaN(person.long) &&
          person.lat !== 0 &&
          person.long !== 0;

        if (!hasValidLocation) {
          return false;
        }

        // Then check if person matches search query
        return (
          person.name.toLowerCase().includes(query) ||
          person.bio.toLowerCase().includes(query) ||
          person.interests.some((interest) => interest.toLowerCase().includes(query))
        );
      });
    }
  }, [searchQuery, MapType, filteredEvents, People]);

  // Handle item press - memoized to prevent recreation
  const handleItemPress = useCallback(
    (item: EventType | Person) => {
      if (MapType === MapTsTypeMap.Events) {
        const event = item as EventType;
        setSelectedEvent(event);
        const coordinates = {
          latitude: event.locationData.coordinates[1],
          longitude: event.locationData.coordinates[0],
        };
        zoomToLocation(coordinates);
      } else {
        const person = item as Person;
        const coordinates = {
          latitude: person.lat,
          longitude: person.long,
        };
        zoomToLocation(coordinates);
      }
      onClose();
    },
    [MapType, setSelectedEvent, zoomToLocation, onClose]
  );

  // Handle friend request - memoized to prevent recreation
  const handleFriendRequest = useCallback((person: Person) => {
    Alert.alert('Friend Request', `Friend request sent to ${person.name}`, [{ text: 'OK' }]);
  }, []);

  // Memoize render item functions to prevent recreation on every render
  const renderEventItem = useCallback(
    ({ item }: { item: EventType }) => (
      <EventItem item={item} onPress={handleItemPress} colors={colors} />
    ),
    [handleItemPress, colors]
  );

  const renderPersonItem = useCallback(
    ({ item }: { item: Person }) => (
      <PersonItem
        item={item}
        onPress={handleItemPress}
        onFriendRequest={handleFriendRequest}
        colors={colors}
        isDark={isDark}
      />
    ),
    [handleItemPress, handleFriendRequest, colors, isDark]
  );

  // Memoize empty list component to prevent recreation
  const emptyListComponent = useMemo(
    () => <EmptyListComponent searchQuery={searchQuery} MapType={MapType} colors={colors} />,
    [searchQuery, MapType, colors]
  );

  // Memoize key extractor to prevent recreation
  const keyExtractor = useCallback((item: EventType | Person) => item.id.toString(), []);

  return (
    <BottomSheetModal
      ref={bottomSheetModalRef}
      index={0}
      snapPoints={snapPoints}
      onChange={handleSheetChanges}
      enableContentPanningGesture={false}
      backdropComponent={renderBackdrop}
      backgroundStyle={{ backgroundColor: colors.background }}
      keyboardBehavior="interactive"
      enableOverDrag={false}
      handleIndicatorStyle={{ backgroundColor: colors.grey }}>
      <BottomSheetView className="flex-1" style={{ backgroundColor: colors.background }}>
        <View
          className="flex-row items-center justify-between border-b px-4 py-4"
          style={{ borderBottomColor: colors.grey5 }}>
          <Text className="text-lg font-semibold" style={{ color: colors.foreground }}>
            Search {MapType === MapTsTypeMap.Events ? 'Events' : 'People'}
          </Text>
          <TouchableOpacity className="p-2" onPress={onClose}>
            <Ionicons name="close" size={24} color={colors.foreground} />
          </TouchableOpacity>
        </View>

        <Box className="p-4">
          <Input
            variant="outline"
            className="h-14 rounded-xl border-0"
            style={{ backgroundColor: colors.grey5 }}>
            <InputSlot className="pl-3">
              <Ionicons name="search" size={20} color={colors.grey} />
            </InputSlot>
            <InputField
              placeholder={`Search ${MapType === MapTsTypeMap.Events ? 'events' : 'people'}...`}
              value={searchQuery}
              onChangeText={setSearchQuery}
              className={`font-medium placeholder:text-[${colors.grey}] ${isDark ? 'text-white' : 'text-black'}`}
              placeholderTextColor={isDark ? colors.grey : colors.grey}
            />
            {searchQuery ? (
              <InputSlot className="pr-3">
                <TouchableOpacity onPress={handleClearSearch}>
                  <Ionicons name="close-circle" size={20} color={colors.grey} />
                </TouchableOpacity>
              </InputSlot>
            ) : null}
          </Input>
        </Box>

        {loading ? (
          <View className="flex-1 items-center justify-center">
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        ) : (
          <FlatList
            data={filteredItems as any}
            keyExtractor={keyExtractor}
            renderItem={
              MapType === MapTsTypeMap.Events ? renderEventItem : (renderPersonItem as any)
            }
            className="flex-1"
            contentContainerStyle={{ paddingBottom: Platform.OS === 'android' ? 100 : 40 }}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={emptyListComponent}
            removeClippedSubviews
            maxToRenderPerBatch={10}
            windowSize={10}
            scrollEventThrottle={16}
            overScrollMode="never"
            bounces={false}
            initialNumToRender={10}
          />
        )}
      </BottomSheetView>
    </BottomSheetModal>
  );
};

// Wrap the component with memo to prevent unnecessary rerenders
export default memo(SearchBottomSheet);
